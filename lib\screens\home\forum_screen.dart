import 'package:flutter/material.dart';

class ForumScreen extends StatelessWidget {
  const ForumScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final topics = [
      {
        'title': 'Bảo vệ môi trường',
        'content': '<PERSON><PERSON><PERSON> có ý tưởng gì để giảm rác thải nhựa không?',
        'author': 'greenuser',
        'createdAt': '2 hours ago',
        'replies': 3,
        'views': 15,
      },
      {
        'title': 'Tết và môi trường',
        'content': 'Làm sao để tổ chức Tết mà không gây ô nhiễm?',
        'author': 'acmatvjrus',
        'createdAt': '1 hour ago',
        'replies': 1,
        'views': 8,
      },
      {
        'title': 'Chia sẻ kinh nghiệm phân loại rác',
        'content': 'Mọi người có thể chia sẻ cách phân loại rác tại nhà không?',
        'author': 'eco_life',
        'createdAt': '30 minutes ago',
        'replies': 2,
        'views': 12,
      },
      {
        'title': 'S<PERSON> kiện trồng cây cuối tuần',
        'content': 'Ai sẽ tham gia sự kiện trồng cây vào cuối tuần này?',
        'author': 'treeplanter',
        'createdAt': '10 minutes ago',
        'replies': 0,
        'views': 5,
      },
    ];

    void openTopicDetail(BuildContext context, Map<String, dynamic> topic) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (_) => TopicDetailScreen(topic: topic),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Danh sách chủ đề', style: TextStyle(fontWeight: FontWeight.bold)),
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 16, top: 8, bottom: 8),
            child: ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF43CEA2),
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 0),
                elevation: 0,
              ),
              onPressed: () {},
              child: const Text('Tạo chủ đề mới', style: TextStyle(fontWeight: FontWeight.bold)),
            ),
          ),
        ],
        backgroundColor: Colors.white,
        elevation: 0,
      ),
      backgroundColor: const Color(0xFFF7F8FA),
      body: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: topics.length,
        itemBuilder: (context, index) {
          final topic = topics[index];
          return Card(
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
            elevation: 0,
            margin: const EdgeInsets.only(bottom: 18),
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      InkWell(
                        onTap: () => openTopicDetail(context, topic),
                        child: Text(
                          topic['title'] as String,
                          style: const TextStyle(
                            color: Color(0xFF1E88E5),
                            fontWeight: FontWeight.bold,
                            fontSize: 18,
                            decoration: TextDecoration.underline,
                          ),
                        ),
                      ),
                      Text(
                        topic['createdAt'] as String,
                        style: const TextStyle(color: Colors.black45, fontSize: 13),
                      ),
                    ],
                  ),
                  const SizedBox(height: 6),
                  Text(topic['content'] as String, style: const TextStyle(fontSize: 15)),
                  const SizedBox(height: 14),
                  Row(
                    children: [
                      Text('Đăng bởi: ', style: TextStyle(color: Colors.black54, fontSize: 13)),
                      Text(topic['author'] as String, style: const TextStyle(color: Color(0xFF43CEA2), fontWeight: FontWeight.w500, fontSize: 13)),
                      const SizedBox(width: 18),
                      Text('Phản hồi: ${topic['replies']}', style: const TextStyle(color: Colors.black54, fontSize: 13)),
                      const SizedBox(width: 18),
                      Text('Lượt xem: ${topic['views']}', style: const TextStyle(color: Colors.black54, fontSize: 13)),
                    ],
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}

class TopicDetailScreen extends StatefulWidget {
  final Map<String, dynamic> topic;
  const TopicDetailScreen({super.key, required this.topic});

  @override
  State<TopicDetailScreen> createState() => _TopicDetailScreenState();
}

class _TopicDetailScreenState extends State<TopicDetailScreen> {
  final List<Map<String, String>> comments = [
    // {'author': 'user1', 'content': 'Bài viết rất hay!', 'createdAt': '1 minute ago'},
    // {'author': 'eco_life', 'content': 'Tôi đồng ý với ý kiến này.', 'createdAt': 'just now'},
  ];
  final TextEditingController _controller = TextEditingController();

  void addComment() {
    final text = _controller.text.trim();
    if (text.isEmpty) return;
    setState(() {
      comments.add({
        'author': 'bạn',
        'content': text,
        'createdAt': 'just now',
      });
      _controller.clear();
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.topic['title'] ?? 'Chi tiết chủ đề'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(widget.topic['title'] ?? '', style: const TextStyle(fontSize: 22, fontWeight: FontWeight.bold)),
            const SizedBox(height: 12),
            Row(
              children: [
                Text('Đăng bởi: ', style: const TextStyle(fontSize: 15)),
                Text(widget.topic['author'] ?? '', style: const TextStyle(color: Color(0xFF43CEA2), fontWeight: FontWeight.w500, fontSize: 15)),
                const SizedBox(width: 18),
                Text(widget.topic['createdAt'] ?? '', style: const TextStyle(fontSize: 15, color: Colors.black54)),
              ],
            ),
            const SizedBox(height: 8),
            Text(widget.topic['content'] ?? '', style: const TextStyle(fontSize: 16)),
            const SizedBox(height: 24),
            const Divider(),
            const SizedBox(height: 8),
            const Text('Phản hồi', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 17, color: Color(0xFF1E88E5))),
            const SizedBox(height: 8),
            if (comments.isEmpty)
              const Text('Chưa có phản hồi nào.', style: TextStyle(color: Colors.black54)),
            if (comments.isNotEmpty)
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: comments.length,
                separatorBuilder: (_, __) => const Divider(height: 24),
                itemBuilder: (context, idx) {
                  final cmt = comments[idx];
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(cmt['author'] ?? '', style: const TextStyle(color: Color(0xFF43CEA2), fontWeight: FontWeight.w500)),
                          const SizedBox(width: 12),
                          Text(cmt['createdAt'] ?? '', style: const TextStyle(color: Colors.black54, fontSize: 12)),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(cmt['content'] ?? ''),
                    ],
                  );
                },
              ),
            const SizedBox(height: 24),
            const Divider(),
            const SizedBox(height: 8),
            const Text('Viết phản hồi', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16, color: Color(0xFF43CEA2))),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border.all(color: const Color(0xFF43CEA2), width: 1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: TextField(
                controller: _controller,
                minLines: 2,
                maxLines: 4,
                decoration: const InputDecoration(
                  border: InputBorder.none,
                  hintText: 'Nhập nội dung phản hồi...',
                ),
              ),
            ),
            const SizedBox(height: 10),
            Row(
              children: [
                ElevatedButton(
                  onPressed: addComment,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF43CEA2),
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                  ),
                  child: const Text('Gửi phản hồi'),
                ),
                const SizedBox(width: 8),
                IconButton(
                  onPressed: () {},
                  icon: const Icon(Icons.emoji_emotions_outlined, color: Color(0xFF43CEA2)),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
} 