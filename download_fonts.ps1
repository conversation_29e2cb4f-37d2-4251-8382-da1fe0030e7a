# PowerShell script to download Inter font files
# Run this script to download the Inter font files for the EcoSolves app

Write-Host "Downloading Inter font files..." -ForegroundColor Green

# Create fonts directory if it doesn't exist
if (!(Test-Path "fonts")) {
    New-Item -ItemType Directory -Path "fonts"
    Write-Host "Created fonts directory" -ForegroundColor Yellow
}

# Download Inter font files
$fontUrls = @{
    "Inter-Regular.ttf" = "https://github.com/rsms/inter/raw/master/docs/font-files/Inter-Regular.ttf"
    "Inter-Medium.ttf" = "https://github.com/rsms/inter/raw/master/docs/font-files/Inter-Medium.ttf"
    "Inter-SemiBold.ttf" = "https://github.com/rsms/inter/raw/master/docs/font-files/Inter-SemiBold.ttf"
    "Inter-Bold.ttf" = "https://github.com/rsms/inter/raw/master/docs/font-files/Inter-Bold.ttf"
}

foreach ($font in $fontUrls.GetEnumerator()) {
    $fileName = $font.Key
    $url = $font.Value
    $outputPath = "fonts\$fileName"
    
    Write-Host "Downloading $fileName..." -ForegroundColor Cyan
    
    try {
        Invoke-WebRequest -Uri $url -OutFile $outputPath -UseBasicParsing
        Write-Host "✓ Downloaded $fileName" -ForegroundColor Green
    }
    catch {
        Write-Host "✗ Failed to download $fileName" -ForegroundColor Red
        Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`nFont download completed!" -ForegroundColor Green
Write-Host "`nNext steps:" -ForegroundColor Yellow
Write-Host "1. Uncomment the fonts section in pubspec.yaml" -ForegroundColor White
Write-Host "2. Update all fontFamily references from 'Roboto' to 'Inter'" -ForegroundColor White
Write-Host "3. Run 'flutter pub get'" -ForegroundColor White
Write-Host "4. Run 'flutter clean && flutter pub get'" -ForegroundColor White 