@if($languages->count() > 1)
    @if($style === 'dropdown')
        <!-- Dropdown Style -->
        <div class="relative inline-block text-left" x-data="{ open: false }">
            <div>
                <button @click="open = !open" type="button" 
                        class="inline-flex justify-center w-full rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" 
                        id="language-menu" aria-haspopup="true" :aria-expanded="open">
                    @if($currentLanguage)
                        <span class="mr-2">{{ get_flag_emoji($currentLanguage->locale) }}</span>
                        {{ $currentLanguage->name }}
                    @else
                        <span class="mr-2">🌐</span>
                        Language
                    @endif
                    <svg class="-mr-1 ml-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                    </svg>
                </button>
            </div>

            <div x-show="open" @click.away="open = false" x-transition:enter="transition ease-out duration-100" x-transition:enter-start="transform opacity-0 scale-95" x-transition:enter-end="transform opacity-100 scale-100" x-transition:leave="transition ease-in duration-75" x-transition:leave-start="transform opacity-100 scale-100" x-transition:leave-end="transform opacity-0 scale-95" 
                 class="origin-top-right absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50">
                <div class="py-1" role="menu" aria-orientation="vertical" aria-labelledby="language-menu">
                    @foreach($languages as $language)
                        <a href="{{ get_localized_url($language->locale) }}"
                           class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 {{ $language->locale === $currentLocale ? 'bg-gray-50 font-medium' : '' }}"
                           role="menuitem">
                            <span class="mr-3">{{ get_flag_emoji($language->locale) }}</span>
                            {{ $language->name }}
                            @if($language->locale === $currentLocale)
                                <svg class="ml-auto h-4 w-4 text-indigo-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                </svg>
                            @endif
                        </a>
                    @endforeach
                </div>
            </div>
        </div>

    @elseif($style === 'buttons')
        <!-- Button Style -->
        <div class="flex space-x-2">
            @foreach($languages as $language)
                <a href="{{ get_localized_url($language->locale) }}"
                   class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 {{ $language->locale === $currentLocale ? 'bg-indigo-50 border-indigo-300 text-indigo-700' : '' }}">
                    <span class="mr-2">{{ get_flag_emoji($language->locale) }}</span>
                    {{ $language->name }}
                </a>
            @endforeach
        </div>

    @elseif($style === 'select')
        <!-- Select Style -->
        <select onchange="window.location.href = this.value"
                class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
            @foreach($languages as $language)
                <option value="{{ get_localized_url($language->locale) }}" {{ $language->locale === $currentLocale ? 'selected' : '' }}>
                    {{ get_flag_emoji($language->locale) }} {{ $language->name }}
                </option>
            @endforeach
        </select>

    @elseif($style === 'flags')
        <!-- Flag Only Style -->
        <div class="flex space-x-2">
            @foreach($languages as $language)
                <a href="{{ get_localized_url($language->locale) }}"
                   class="inline-flex items-center justify-center w-8 h-8 rounded-full hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 {{ $language->locale === $currentLocale ? 'bg-indigo-50 ring-2 ring-indigo-300' : '' }}"
                   title="{{ $language->name }}">
                    <span class="text-lg">{{ get_flag_emoji($language->locale) }}</span>
                </a>
            @endforeach
        </div>

    @elseif($style === 'minimal')
        <!-- Minimal Style -->
        <div class="flex items-center space-x-1 text-sm">
            @foreach($languages as $language)
                @if($language->locale === $currentLocale)
                    <span class="px-2 py-1 bg-indigo-100 text-indigo-800 rounded font-medium">
                        {{ strtoupper($language->locale) }}
                    </span>
                @else
                    <a href="{{ get_localized_url($language->locale) }}"
                       class="px-2 py-1 text-gray-600 hover:text-indigo-600 hover:bg-gray-100 rounded transition-colors">
                        {{ strtoupper($language->locale) }}
                    </a>
                @endif
                @if(!$loop->last)
                    <span class="text-gray-400">|</span>
                @endif
            @endforeach
        </div>
    @endif
@endif

@push('scripts')
<script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
@endpush
