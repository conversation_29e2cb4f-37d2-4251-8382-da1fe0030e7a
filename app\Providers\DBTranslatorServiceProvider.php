<?php

namespace App\Providers;

use App\Models\Translation;
use Illuminate\Translation\TranslationServiceProvider;

class DBTranslatorServiceProvider extends TranslationServiceProvider
{
    public function register()
    {
        $this->app->singleton('translator', function ($app) {
            $locale = $app['config']['app.locale'];

            return new Translation($app['db'], $locale);
        });
    }
}
