import 'package:flutter/material.dart';
import '../constants/colors.dart';

class EcoAppBar extends StatelessWidget implements PreferredSizeWidget {
  final Widget? title;
  final VoidCallback? onNotification;
  final Widget? userMenu;
  final List<Widget>? actions;

  const EcoAppBar({
    super.key,
    this.title,
    this.onNotification,
    this.userMenu,
    this.actions,
  });

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);

  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: AppColors.background,
      elevation: 0,
      titleSpacing: 0,
      leadingWidth: 0,
      title: Row(
        children: [
          const SizedBox(width: 16),
          title ?? const SizedBox(),
          const Spacer(),
          if (onNotification != null)
            IconButton(
              icon: Icon(
                Icons.notifications_none, 
                color: AppColors.primary, 
                size: 28
              ),
              onPressed: onNotification,
            ),
          if (userMenu != null) userMenu!,
          if (actions != null) ...actions!,
          const SizedBox(width: 8),
        ],
      ),
    );
  }
} 