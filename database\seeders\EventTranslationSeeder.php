<?php

namespace Database\Seeders;

use App\Models\Translation;
use Illuminate\Database\Seeder;

class EventTranslationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $translations = [
            'general.thoi_gian' => ['vi' => 'Thời gian', 'en' => 'Time'],
            'general.dia_diem' => ['vi' => 'Địa điểm', 'en' => 'Location'],
            'general.sap_dien_ra' => ['vi' => 'Sắp diễn ra', 'en' => 'Upcoming'],
            'general.ket_thuc' => ['vi' => 'Kết thúc', 'en' => 'Ended'],
            'general.dang_dien_ra' => ['vi' => 'Đang diễn ra', 'en' => 'Ongoing'],
            'general.danh_sach_su_kien' => ['vi' => 'Danh sách sự kiện', 'en' => 'Event List'],
        ];

        foreach ($translations as $key => $values) {
            foreach ($values as $locale => $value) {
                Translation::updateOrCreate(
                    ['key' => $key, 'locale' => $locale],
                    ['value' => $value]
                );
            }
        }

        $this->command->info('Added event translations successfully!');
    }
}
