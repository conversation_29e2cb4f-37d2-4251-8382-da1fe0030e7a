import 'package:flutter/material.dart';
import '../../components/account_menu_list.dart';
import 'profile_screen.dart';
import 'event_menu_screen.dart';
import 'gift_screen.dart';

class StatisticScreen extends StatelessWidget {
  const StatisticScreen({super.key});

  @override
  Widget build(BuildContext context) {
    // Mock data
    final stats = [
      {'label': 'Tổng báo cáo', 'value': 0, 'color': const Color(0xFF0EA5E9)},
      {'label': 'Báo cáo đã duyệt', 'value': 0, 'color': const Color(0xFF10B981)},
      {'label': 'Lần đổi quà', 'value': 0, 'color': const Color(0xFFfacc15)},
      {'label': 'Điểm đã tích', 'value': 5, 'color': const Color(0xFF8B5CF6)},
    ];
    final reportStats = [
      {'label': 'Đã duyệt:', 'value': 0, 'color': const Color(0xFF10B981)},
      {'label': 'Đang chờ:', 'value': 0, 'color': const Color(0xFFfacc15)},
      {'label': 'Bị từ chối:', 'value': 0, 'color': Colors.red},
    ];
    final giftStats = [
      {'label': 'Tổng lần đổi:', 'value': 0, 'color': const Color(0xFF0EA5E9)},
      {'label': 'Đã duyệt:', 'value': 0, 'color': const Color(0xFF10B981)},
      {'label': 'Đã hoàn thành:', 'value': 0, 'color': const Color(0xFF8B5CF6)},
    ];
    final pointHistory = [
      {
        'desc': 'Cộng điểm khi đăng nhập',
        'date': '01/07/2025 07:48',
        'point': 5,
      },
    ];

    return Scaffold(
      appBar: AppBar(
        title: const Text('Thống kê'),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Thống kê tổng quan', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18)),
            const SizedBox(height: 12),
            GridView.count(
              crossAxisCount: 2,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              mainAxisSpacing: 12,
              crossAxisSpacing: 12,
              childAspectRatio: 2.2,
              children: stats.map((s) => Container(
                padding: const EdgeInsets.symmetric(vertical: 18),
                decoration: BoxDecoration(
                  color: (s['color'] as Color).withOpacity(0.07),
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(color: (s['color'] as Color).withOpacity(0.15)),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      '${s['value']}',
                      style: TextStyle(
                        color: s['color'] as Color,
                        fontWeight: FontWeight.bold,
                        fontSize: 22,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      s['label'] as String,
                      style: const TextStyle(fontSize: 14, color: Colors.black87),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              )).toList(),
            ),
            const SizedBox(height: 18),
            Row(
              children: [
                // Thống kê báo cáo
                Expanded(
                  child: Container(
                    margin: const EdgeInsets.only(right: 8),
                    padding: const EdgeInsets.all(14),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(color: Colors.grey[200]!),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text('Thống kê báo cáo', style: TextStyle(fontWeight: FontWeight.bold)),
                        const SizedBox(height: 8),
                        ...reportStats.map((r) => Padding(
                          padding: const EdgeInsets.symmetric(vertical: 2),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(r['label'] as String, style: const TextStyle(fontSize: 14)),
                              Text('${r['value']}', style: TextStyle(color: r['color'] as Color, fontWeight: FontWeight.bold)),
                            ],
                          ),
                        )),
                      ],
                    ),
                  ),
                ),
                // Thống kê đổi quà
                Expanded(
                  child: Container(
                    margin: const EdgeInsets.only(left: 8),
                    padding: const EdgeInsets.all(14),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(color: Colors.grey[200]!),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text('Thống kê đổi quà', style: TextStyle(fontWeight: FontWeight.bold)),
                        const SizedBox(height: 8),
                        ...giftStats.map((g) => Padding(
                          padding: const EdgeInsets.symmetric(vertical: 2),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(g['label'] as String, style: const TextStyle(fontSize: 14)),
                              Text('${g['value']}', style: TextStyle(color: g['color'] as Color, fontWeight: FontWeight.bold)),
                            ],
                          ),
                        )),
                      ],
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            const Text('Lịch sử tích điểm gần đây', style: TextStyle(fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            ...pointHistory.map((p) => Container(
              margin: const EdgeInsets.only(bottom: 12),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[200]!),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(p['desc'] as String, style: const TextStyle(fontWeight: FontWeight.w500)),
                      const SizedBox(height: 2),
                      Text(p['date'] as String, style: const TextStyle(color: Colors.black54, fontSize: 13)),
                    ],
                  ),
                  Text('+${p['point']} điểm', style: const TextStyle(color: Colors.green, fontWeight: FontWeight.bold)),
                ],
              ),
            )),
            if (pointHistory.isEmpty)
              Container(
                margin: const EdgeInsets.only(bottom: 12),
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 18),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Center(child: Text('Chưa có lịch sử tích điểm', style: TextStyle(color: Colors.black54))),
              ),
            const SizedBox(height: 18),
            const Text('Lịch sử báo cáo gần đây', style: TextStyle(fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            Container(
              margin: const EdgeInsets.only(bottom: 12),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 18),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Center(child: Text('Chưa có lịch sử báo cáo', style: TextStyle(color: Colors.black54))),
            ),
            const SizedBox(height: 18),
            const Text('Lịch sử đổi quà gần đây', style: TextStyle(fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            Container(
              margin: const EdgeInsets.only(bottom: 12),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 18),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Center(child: Text('Chưa có lịch sử đổi quà', style: TextStyle(color: Colors.black54))),
            ),
            const Divider(height: 32),
            AccountMenuList(
              activeIndex: 1,
              onMenuTap: (context, index) {
                switch (index) {
                  case 0:
                    Navigator.of(context).pushReplacement(
                      MaterialPageRoute(builder: (context) => const ProfileScreen()),
                    );
                    break;
                  case 1:
                    // Đã ở Thống kê
                    break;
                  case 2:
                    Navigator.of(context).pushReplacement(
                      MaterialPageRoute(builder: (context) => const EventMenuScreen()),
                    );
                    break;
                  case 3:
                    Navigator.of(context).pushReplacement(
                      MaterialPageRoute(builder: (context) => const GiftScreen()),
                    );
                    break;
                  case 4:
                    Navigator.of(context).pushNamedAndRemoveUntil('/', (route) => false);
                    break;
                }
              },
            ),
          ],
        ),
      ),
    );
  }
} 