<?php

namespace App\Filament\Resources\TranslationResource\Pages;

use App\Filament\Resources\TranslationResource;
use App\Models\Language;
use App\Models\Translation;
use Filament\Actions;
use Filament\Forms;
use Filament\Resources\Pages\ListRecords;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Artisan;

class ListTranslations extends ListRecords
{
    protected static string $resource = TranslationResource::class;

    /**
     * Get default export path based on OS
     */
    private function getDefaultExportPath(): string
    {
        return PHP_OS_FAMILY === 'Windows'
            ? 'C:\\translations'
            : '/tmp/translations';
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),

            Actions\Action::make('download_all')
                ->label('Download All Translations')
                ->icon('heroicon-o-cloud-arrow-down')
                ->color('info')
                ->url(function () {
                    // Generate fresh export
                    \Artisan::call('translations:export-all');
                    return route('download.translations');
                })
                ->openUrlInNewTab(),

            Actions\Action::make('import_json')
                ->label('Import JSON')
                ->icon('heroicon-o-arrow-up-tray')
                ->color('success')
                ->form([
                    Forms\Components\Select::make('locale')
                        ->label('Target Language')
                        ->required()
                        ->options(function () {
                            return Language::where('is_active', true)
                                ->pluck('name', 'locale')
                                ->toArray();
                        })
                        ->searchable()
                        ->preload(),

                    Forms\Components\FileUpload::make('json_file')
                        ->label('JSON File')
                        ->required()
                        ->acceptedFileTypes(['application/json', 'text/plain'])
                        ->maxSize(5120) // 5MB
                        ->disk('public')
                        ->directory('temp-uploads')
                        ->visibility('private')
                        ->helperText('Upload a JSON file with key-value translations. Large files may take a few minutes to process.'),

                    Forms\Components\Toggle::make('overwrite_existing')
                        ->label('Overwrite Existing Translations')
                        ->default(false)
                        ->helperText('If enabled, existing translations will be updated'),
                ])
                ->action(function (array $data) {
                    // Increase time and memory limits for large imports
                    set_time_limit(300); // 5 minutes
                    ini_set('memory_limit', '1G'); // Increase memory for large files

                    try {
                        // Handle file upload - Filament stores as array
                        $uploadedFile = $data['json_file'];

                        if (empty($uploadedFile)) {
                            throw new \Exception('No file uploaded');
                        }

                        // Get the first file if it's an array
                        $filePath = is_array($uploadedFile) ? $uploadedFile[0] : $uploadedFile;

                        // Check if file exists
                        if (!Storage::disk('public')->exists($filePath)) {
                            throw new \Exception('Uploaded file not found: ' . $filePath);
                        }

                        // Get the uploaded file content directly
                        $jsonContent = Storage::disk('public')->get($filePath);

                        // Remove BOM if exists
                        if (substr($jsonContent, 0, 3) === "\xEF\xBB\xBF") {
                            $jsonContent = substr($jsonContent, 3);
                        }

                        // Trim whitespace
                        $jsonContent = trim($jsonContent);

                        if (empty($jsonContent)) {
                            throw new \Exception('File is empty');
                        }

                        $translations = json_decode($jsonContent, true);

                        if (json_last_error() !== JSON_ERROR_NONE) {
                            throw new \Exception('Invalid JSON format: ' . json_last_error_msg() . '. Please check your JSON syntax.');
                        }

                        if (!is_array($translations)) {
                            throw new \Exception('JSON must contain an object with key-value pairs');
                        }

                        if (empty($translations)) {
                            throw new \Exception('JSON file contains no translations');
                        }

                        $imported = 0;
                        $skipped = 0;
                        $updated = 0;

                        // Prepare data for upsert operation
                        $upsertData = [];
                        $existingKeys = [];

                        if (!$data['overwrite_existing']) {
                            // Get existing keys only if we need to skip them
                            $existingKeys = Translation::where('locale', $data['locale'])
                                ->pluck('key')
                                ->toArray();
                        }

                        foreach ($translations as $key => $value) {
                            if (!is_string($value)) {
                                continue; // Skip non-string values
                            }

                            // Skip existing if overwrite is disabled
                            if (!$data['overwrite_existing'] && in_array($key, $existingKeys)) {
                                $skipped++;
                                continue;
                            }

                            $upsertData[] = [
                                'key' => $key,
                                'locale' => $data['locale'],
                                'value' => $value,
                                'created_at' => now(),
                                'updated_at' => now(),
                            ];
                        }

                        // Use upsert for maximum performance (insert or update in one operation)
                        if (!empty($upsertData)) {
                            $chunks = array_chunk($upsertData, 500); // Larger chunks for upsert

                            foreach ($chunks as $chunk) {
                                $result = Translation::upsert(
                                    $chunk,
                                    ['key', 'locale'], // Unique columns
                                    ['value', 'updated_at'] // Columns to update
                                );

                                // Count operations (approximate)
                                $chunkCount = count($chunk);
                                if ($data['overwrite_existing']) {
                                    $updated += $chunkCount; // May include some new records
                                } else {
                                    $imported += $chunkCount;
                                }
                            }
                        }

                        // Clean up uploaded file
                        Storage::disk('public')->delete($filePath);

                        // Clear only translation cache for the specific locale
                        try {
                            $cachePattern = "trans_{$data['locale']}_*";

                            // Try to clear specific cache keys if Redis is available
                            if (Cache::getStore() instanceof \Illuminate\Cache\RedisStore) {
                                $redis = Cache::getStore()->connection();
                                $prefix = Cache::getStore()->getPrefix();
                                $keys = $redis->keys($prefix . $cachePattern);
                                if (!empty($keys)) {
                                    $redis->del($keys);
                                }
                            } else {
                                // Fallback to full cache flush for other drivers
                                Cache::flush();
                            }
                        } catch (\Exception $e) {
                            \Log::warning('Cache clear failed: ' . $e->getMessage());
                            // Continue without failing the import
                        }

                        // Regenerate only the specific locale file for faster processing
                        try {
                            $this->regenerateLocaleFile($data['locale']);
                        } catch (\Exception $e) {
                            \Log::warning('Failed to regenerate translation file for locale ' . $data['locale'] . ': ' . $e->getMessage());

                            // Fallback to full regeneration
                            try {
                                Artisan::call('translations:export-all');
                            } catch (\Exception $e2) {
                                \Log::warning('Failed to regenerate all translation files: ' . $e2->getMessage());
                            }
                        }

                        Notification::make()
                            ->title('Import completed successfully')
                            ->body("Imported: {$imported}, Updated: {$updated}, Skipped: {$skipped}")
                            ->success()
                            ->send();

                    } catch (\Exception $e) {
                        // Clean up uploaded file on error
                        if (isset($filePath)) {
                            Storage::disk('public')->delete($filePath);
                        }

                        Notification::make()
                            ->title('Import failed')
                            ->body($e->getMessage())
                            ->danger()
                            ->send();
                    }
                }),

            Actions\Action::make('export_json')
                ->label('Export JSON')
                ->icon('heroicon-o-arrow-down-tray')
                ->color('info')
                ->form([
                    Forms\Components\Select::make('locale')
                        ->label('Language to Export')
                        ->required()
                        ->options(function () {
                            return Language::where('is_active', true)
                                ->pluck('name', 'locale')
                                ->toArray();
                        })
                        ->searchable()
                        ->preload(),

                    Forms\Components\TextInput::make('export_path')
                        ->label('Export Directory Path (Optional)')
                        ->placeholder('Leave empty to use default: ' . (PHP_OS_FAMILY === 'Windows' ? 'C:\\translations' : '/tmp/translations'))
                        ->helperText('Full absolute path to directory where you want to save the file. Leave empty to use default location. Examples: C:\\MyProject\\translations (Windows) or /home/<USER>/exports (Linux/Mac). Directory will be created if it doesn\'t exist.')
                        ->suffixIcon('heroicon-o-folder')
                        ->validationAttribute('export path')
                        ->rules([
                            function () {
                                return function (string $attribute, $value, \Closure $fail) {
                                    // Skip validation if empty (will use default path)
                                    if (empty($value)) {
                                        return;
                                    }

                                    // Check for dangerous patterns
                                    $dangerousPatterns = ['..', '<', '>', '|', '*', '?'];
                                    foreach ($dangerousPatterns as $pattern) {
                                        if (str_contains($value, $pattern)) {
                                            $fail('Export path contains invalid characters.');
                                            return;
                                        }
                                    }

                                    // Try to resolve the path
                                    $realPath = realpath(dirname($value));
                                    if ($realPath === false && !is_dir(dirname($value))) {
                                        // Check if parent directory exists or can be created
                                        $parentDir = dirname($value);
                                        if (!is_dir($parentDir)) {
                                            $fail('Parent directory does not exist and cannot be accessed: ' . $parentDir);
                                        }
                                    }
                                };
                            }
                        ]),
                ])
                ->action(function (array $data) {
                    try {
                        $translations = Translation::where('locale', $data['locale'])
                            ->pluck('value', 'key')
                            ->toArray();

                        if (empty($translations)) {
                            Notification::make()
                                ->title('No translations found')
                                ->body("No translations found for locale: {$data['locale']}")
                                ->warning()
                                ->send();
                            return;
                        }

                        // Prepare export directory path
                        $exportPath = $data['export_path'] ?? '';

                        // Use default path if not provided
                        if (empty($exportPath)) {
                            $exportPath = $this->getDefaultExportPath();
                        }

                        $exportDirectory = rtrim($exportPath, '/\\');

                        // Validate directory exists or can be created
                        if (!is_dir($exportDirectory)) {
                            if (!mkdir($exportDirectory, 0755, true)) {
                                throw new \Exception("Cannot create directory: {$exportDirectory}");
                            }
                        }

                        // Check if directory is writable
                        if (!is_writable($exportDirectory)) {
                            throw new \Exception("Directory is not writable: {$exportDirectory}");
                        }

                        // Generate filename
                        $filename = "translations_{$data['locale']}_" . date('Y-m-d_H-i-s') . '.json';
                        $fullPath = $exportDirectory . DIRECTORY_SEPARATOR . $filename;

                        $jsonContent = json_encode($translations, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);

                        // Write file directly to external path
                        if (file_put_contents($fullPath, $jsonContent) === false) {
                            throw new \Exception("Failed to write file: {$fullPath}");
                        }

                        $isDefaultPath = empty($data['export_path']);
                        $pathInfo = $isDefaultPath ? ' (using default path)' : '';

                        Notification::make()
                            ->title('Export completed')
                            ->body("Exported " . count($translations) . " translations to: {$fullPath}{$pathInfo}")
                            ->success()
                            ->persistent()
                            ->send();

                        // Return download response (copy to temp for download)
                        $tempPath = storage_path('app/temp/' . $filename);
                        if (!is_dir(dirname($tempPath))) {
                            mkdir(dirname($tempPath), 0755, true);
                        }
                        copy($fullPath, $tempPath);
                        return response()->download($tempPath, $filename)->deleteFileAfterSend();

                    } catch (\Exception $e) {
                        Notification::make()
                            ->title('Export failed')
                            ->body($e->getMessage())
                            ->danger()
                            ->send();
                    }
                }),

            Actions\Action::make('sync_with_files')
                ->label('Sync with Lang Files')
                ->icon('heroicon-o-arrow-path')
                ->color('warning')
                ->form([
                    Forms\Components\Select::make('direction')
                        ->label('Sync Direction')
                        ->required()
                        ->options([
                            'files-to-db' => 'Files to Database',
                            'db-to-files' => 'Database to Files',
                            'both' => 'Both Directions',
                        ])
                        ->default('files-to-db')
                        ->helperText('Choose how to sync translations'),

                    Forms\Components\Select::make('locale')
                        ->label('Language (optional)')
                        ->options(function () {
                            return Language::where('is_active', true)
                                ->pluck('name', 'locale')
                                ->toArray();
                        })
                        ->searchable()
                        ->preload()
                        ->helperText('Leave empty to sync all active languages'),

                    Forms\Components\Toggle::make('force')
                        ->label('Force Overwrite')
                        ->default(false)
                        ->helperText('Overwrite existing translations'),
                ])
                ->action(function (array $data) {
                    try {
                        $command = 'translations:sync ' . $data['direction'];

                        if (!empty($data['locale'])) {
                            $command .= ' --locale=' . $data['locale'];
                        }

                        if ($data['force']) {
                            $command .= ' --force';
                        }

                        \Artisan::call($command);
                        $output = \Artisan::output();

                        Notification::make()
                            ->title('Sync completed')
                            ->body('Translations synced successfully')
                            ->success()
                            ->send();

                    } catch (\Exception $e) {
                        Notification::make()
                            ->title('Sync failed')
                            ->body($e->getMessage())
                            ->danger()
                            ->send();
                    }
                }),
        ];
    }

    /**
     * Regenerate translation files for specific locale (optimized)
     */
    private function regenerateLocaleFile(string $locale): void
    {
        $translations = Translation::where('locale', $locale)
            ->pluck('value', 'key')
            ->toArray();

        if (empty($translations)) {
            return;
        }

        // Batch file operations
        $this->batchUpdateTranslationFiles($locale, $translations);
    }

    /**
     * Batch update all translation files efficiently
     */
    private function batchUpdateTranslationFiles(string $locale, array $translations): void
    {
        $translationsDir = storage_path('app/translations');

        // Ensure directory exists
        if (!is_dir($translationsDir)) {
            mkdir($translationsDir, 0755, true);
        }

        // Update individual locale file
        $localeFilePath = "{$translationsDir}/{$locale}.json";
        file_put_contents(
            $localeFilePath,
            json_encode($translations, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)
        );

        // Update all_translations.json
        $this->updateAllTranslationsFile($locale, $translations);

        // Update flat translations file (only if needed)
        $this->updateFlatTranslationsFile();
    }

    /**
     * Update all_translations.json file
     */
    private function updateAllTranslationsFile(string $locale, array $translations): void
    {
        $allTranslationsPath = storage_path('app/translations/all_translations.json');

        $allTranslations = [];
        if (file_exists($allTranslationsPath)) {
            $allTranslations = json_decode(file_get_contents($allTranslationsPath), true) ?: [];
        }

        $allTranslations[$locale] = $translations;

        file_put_contents(
            $allTranslationsPath,
            json_encode($allTranslations, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)
        );
    }

    /**
     * Update flat translations file (optimized query)
     */
    private function updateFlatTranslationsFile(): void
    {
        // Use single query instead of loading all translations
        $flatTranslations = Translation::selectRaw("CONCAT(locale, '.', `key`) as flat_key, value")
            ->get()
            ->pluck('value', 'flat_key')
            ->toArray();

        $flatFilePath = storage_path('app/translations/translations_flat.json');
        file_put_contents(
            $flatFilePath,
            json_encode($flatTranslations, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)
        );
    }
}
