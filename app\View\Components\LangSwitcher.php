<?php

namespace App\View\Components;

use App\Models\Language;
use Illuminate\View\Component;
use Illuminate\View\View;

class LangSwitcher extends Component
{
    public $languages;
    public $currentLocale;
    public $currentLanguage;
    public $style;

    /**
     * Create a new component instance.
     */
    public function __construct(string $style = 'dropdown')
    {
        $this->languages = Language::getActive();
        $this->currentLocale = app()->getLocale();
        $this->currentLanguage = $this->languages->where('locale', $this->currentLocale)->first();
        $this->style = $style; // dropdown, buttons, select
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View
    {
        return view('components.lang-switcher');
    }

    /**
     * Get flag emoji for locale
     */
    public function getFlagEmoji(string $locale): string
    {
        $flags = [
            'en' => '🇺🇸',
            'vi' => '🇻🇳',
            'fr' => '🇫🇷',
            'de' => '🇩🇪',
            'es' => '🇪🇸',
            'it' => '🇮🇹',
            'ja' => '🇯🇵',
            'ko' => '🇰🇷',
            'zh' => '🇨🇳',
            'ru' => '🇷🇺',
            'pt' => '🇵🇹',
            'ar' => '🇸🇦',
            'hi' => '🇮🇳',
            'th' => '🇹🇭',
        ];

        return $flags[$locale] ?? '🌐';
    }

    /**
     * Get current route with different locale
     */
    public function getLocalizedUrl(string $locale): string
    {
        $currentRoute = request()->route();

        if (!$currentRoute) {
            return "/{$locale}";
        }

        $routeName = $currentRoute->getName();
        $parameters = $currentRoute->parameters();

        // Replace locale parameter
        $parameters['locale'] = $locale;

        try {
            // Try to generate route with new locale
            return route($routeName, $parameters);
        } catch (\Exception $e) {
            // Fallback: construct URL manually
            $currentUrl = request()->url();
            $currentLocale = app()->getLocale();

            // If current URL has locale prefix, replace it
            if (preg_match('/\/(' . $currentLocale . ')\//', $currentUrl)) {
                return preg_replace('/\/(' . $currentLocale . ')\//', "/{$locale}/", $currentUrl);
            } elseif (preg_match('/\/(' . $currentLocale . ')$/', $currentUrl)) {
                return preg_replace('/\/(' . $currentLocale . ')$/', "/{$locale}", $currentUrl);
            } else {
                // If no locale in URL, add it
                $path = parse_url($currentUrl, PHP_URL_PATH);
                $baseUrl = str_replace($path, '', $currentUrl);
                return $baseUrl . "/{$locale}" . ($path === '/' ? '' : $path);
            }
        }
    }
}
