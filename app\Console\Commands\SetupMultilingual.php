<?php

namespace App\Console\Commands;

use App\Models\Language;
use App\Models\Translation;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class SetupMultilingual extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'multilingual:setup 
                            {--force : Force setup even if data exists}
                            {--seed-translations : Seed basic translations}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Setup multilingual system with basic languages and translations';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $force = $this->option('force');
        $seedTranslations = $this->option('seed-translations');

        $this->info('Setting up multilingual system...');

        // Check if languages already exist
        if (Language::count() > 0 && !$force) {
            $this->warn('Languages already exist. Use --force to override.');
            if (!$this->confirm('Do you want to continue?')) {
                return 0;
            }
        }

        // Setup languages
        $this->setupLanguages($force);

        // Setup translations if requested
        if ($seedTranslations) {
            $this->setupTranslations($force);
        }

        $this->info('Multilingual system setup completed!');
        $this->info('Available commands:');
        $this->line('  php artisan multilingual:status - Check system status');
        $this->line('  php artisan multilingual:convert - Convert entire app to multilingual');
        $this->line('  php artisan translations:extract - Extract texts from views');
        $this->line('  php artisan translations:auto-translate - Auto-translate texts');
        $this->line('  php artisan translations:replace-in-views - Replace hardcoded texts');
        $this->line('  php artisan translations:generate-files - Generate translation files');
        $this->line('  php artisan translations:sync - Sync translations with lang files');

        return 0;
    }

    /**
     * Setup basic languages
     */
    protected function setupLanguages(bool $force): void
    {
        $this->info('Setting up languages...');

        $languages = [
            ['locale' => 'en', 'name' => 'English', 'is_default' => true, 'is_active' => true],
            ['locale' => 'vi', 'name' => 'Tiếng Việt', 'is_default' => false, 'is_active' => true],
        ];

        foreach ($languages as $langData) {
            $existing = Language::where('locale', $langData['locale'])->first();
            
            if ($existing) {
                if ($force) {
                    $existing->update($langData);
                    $this->line("Updated language: {$langData['name']} ({$langData['locale']})");
                } else {
                    $this->line("Language already exists: {$langData['name']} ({$langData['locale']})");
                }
            } else {
                Language::create($langData);
                $this->line("Created language: {$langData['name']} ({$langData['locale']})");
            }
        }
    }

    /**
     * Setup basic translations
     */
    protected function setupTranslations(bool $force): void
    {
        $this->info('Setting up basic translations...');

        $translations = [
            // Navigation
            'nav.home' => ['en' => 'Home', 'vi' => 'Trang chủ'],
            'nav.about' => ['en' => 'About', 'vi' => 'Giới thiệu'],
            'nav.contact' => ['en' => 'Contact', 'vi' => 'Liên hệ'],
            'nav.events' => ['en' => 'Events', 'vi' => 'Sự kiện'],
            'nav.login' => ['en' => 'Login', 'vi' => 'Đăng nhập'],
            'nav.register' => ['en' => 'Register', 'vi' => 'Đăng ký'],
            'nav.logout' => ['en' => 'Logout', 'vi' => 'Đăng xuất'],
            'nav.profile' => ['en' => 'Profile', 'vi' => 'Trang cá nhân'],
            'nav.how_it_works' => ['en' => 'How it works', 'vi' => 'Cách hoạt động'],
            'nav.communities' => ['en' => 'Communities', 'vi' => 'Cộng đồng'],
            'nav.experts' => ['en' => 'Experts', 'vi' => 'Chuyên gia'],

            // Actions
            'action.save' => ['en' => 'Save', 'vi' => 'Lưu'],
            'action.cancel' => ['en' => 'Cancel', 'vi' => 'Hủy'],
            'action.delete' => ['en' => 'Delete', 'vi' => 'Xóa'],
            'action.edit' => ['en' => 'Edit', 'vi' => 'Sửa'],
            'action.create' => ['en' => 'Create', 'vi' => 'Tạo'],
            'action.submit' => ['en' => 'Submit', 'vi' => 'Gửi'],

            // Status
            'status.success' => ['en' => 'Success', 'vi' => 'Thành công'],
            'status.error' => ['en' => 'Error', 'vi' => 'Lỗi'],
            'status.warning' => ['en' => 'Warning', 'vi' => 'Cảnh báo'],
            'status.info' => ['en' => 'Information', 'vi' => 'Thông tin'],

            // Common
            'common.loading' => ['en' => 'Loading...', 'vi' => 'Đang tải...'],
            'common.no_data' => ['en' => 'No data available', 'vi' => 'Không có dữ liệu'],
            'common.confirm' => ['en' => 'Confirm', 'vi' => 'Xác nhận'],
        ];

        $created = 0;
        $updated = 0;
        $skipped = 0;

        foreach ($translations as $key => $localeValues) {
            foreach ($localeValues as $locale => $value) {
                $existing = Translation::where('key', $key)->where('locale', $locale)->first();
                
                if ($existing) {
                    if ($force) {
                        $existing->update(['value' => $value]);
                        $updated++;
                    } else {
                        $skipped++;
                    }
                } else {
                    Translation::create([
                        'key' => $key,
                        'locale' => $locale,
                        'value' => $value,
                    ]);
                    $created++;
                }
            }
        }

        $this->line("Translations: Created {$created}, Updated {$updated}, Skipped {$skipped}");
    }
}
