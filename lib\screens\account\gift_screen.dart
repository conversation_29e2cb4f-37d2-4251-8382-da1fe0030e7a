import 'package:flutter/material.dart';
import '../../components/account_menu_list.dart';
import 'profile_screen.dart';
import 'statistic_screen.dart';
import 'event_menu_screen.dart';

class GiftScreen extends StatelessWidget {
  const GiftScreen({super.key});

  @override
  Widget build(BuildContext context) {
    // Mock data quà tặng
    final gifts = [
      {
        'name': 'Thẻ cào điện thoại 20K',
        'desc': 'Nạp trực tiếp vào số điện thoại. Áp dụng cho các nhà mạng.',
        'point': 50,
        'remain': 20,
      },
      {
        'name': 'Voucher Grab 30K',
        'desc': 'Sử dụng cho dịch vụ GrabCar, GrabBike, GrabFood.',
        'point': 80,
        'remain': 15,
      },
      {
        'name': 'Voucher Shopee 50K',
        'desc': 'Áp dụng cho đơn hàng từ 300K.',
        'point': 120,
        'remain': 10,
      },
      {
        'name': 'Voucher Starbucks 50K',
        'desc': 'Dùng tại tất cả cửa hàng Starbucks.',
        'point': 100,
        'remain': 25,
      },
      {
        'name': 'Voucher CGV 2D',
        'desc': 'Xem phim tại hệ thống rạp CGV.',
        'point': 90,
        'remain': 12,
      },
      {
        'name': 'Sổ tay mini dễ thương',
        'desc': 'Sổ tay nhỏ gọn, tiện lợi mang theo.',
        'point': 40,
        'remain': 30,
      },
      {
        'name': 'Voucher Tiki 100K',
        'desc': 'Áp dụng cho đơn hàng từ 1 triệu.',
        'point': 160,
        'remain': 8,
      },
      {
        'name': 'Voucher Pizza Hut 100K',
        'desc': 'Dùng tại Pizza Hut toàn quốc.',
        'point': 140,
        'remain': 6,
      },
      {
        'name': 'Túi vải canvas',
        'desc': 'Túi vải thời trang, bảo vệ môi trường.',
        'point': 60,
        'remain': 18,
      },
      {
        'name': 'Áo mưa cao cấp',
        'desc': 'Áo mưa chất lượng cao, bền đẹp.',
        'point': 80,
        'remain': 14,
      },
      {
        'name': 'Bộ bút viết cao cấp',
        'desc': 'Bút bi, bút chì, bút dạ, hộp đựng.',
        'point': 60,
        'remain': 20,
      },
      {
        'name': 'Voucher Vinemart 200K',
        'desc': 'Dùng tại hệ thống Vinemart.',
        'point': 200,
        'remain': 4,
      },
      {
        'name': 'Áo thun cotton',
        'desc': 'Áo thun chất liệu cotton, nhiều size.',
        'point': 90,
        'remain': 10,
      },
      {
        'name': 'Bình nước giữ nhiệt',
        'desc': 'Bình giữ nhiệt 500ml, giữ nóng/lạnh lâu.',
        'point': 100,
        'remain': 8,
      },
      {
        'name': 'Mũ bảo hiểm',
        'desc': 'Mũ bảo hiểm thời trang, đạt chuẩn.',
        'point': 120,
        'remain': 7,
      },
    ];
    final giftHistory = [];

    return Scaffold(
      appBar: AppBar(
        title: const Text('Quà tặng'),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Hướng dẫn đổi quà
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              margin: const EdgeInsets.only(bottom: 18),
              decoration: BoxDecoration(
                color: Colors.green[50],
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _giftStep('1', 'Chọn quà', 'Chọn quà phù hợp và đủ điểm tích lũy'),
                  _giftStep('2', 'Xác nhận đổi', 'Xác nhận số lượng, kiểm tra thông tin'),
                  _giftStep('3', 'Chờ duyệt', 'Admin sẽ duyệt và liên hệ để nhận quà'),
                ],
              ),
            ),
            const Text('Quà tặng có thể đổi', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18)),
            const SizedBox(height: 12),
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: gifts.length,
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                mainAxisSpacing: 14,
                crossAxisSpacing: 14,
                childAspectRatio: 0.68,
              ),
              itemBuilder: (context, i) {
                final g = gifts[i];
                return Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.grey[200]!),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Container(
                        height: 70,
                        decoration: BoxDecoration(
                          color: Colors.grey[100],
                          borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
                        ),
                        child: const Center(child: Text('Không có ảnh', style: TextStyle(color: Colors.black45))),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(g['name'] as String, style: const TextStyle(fontWeight: FontWeight.bold)),
                            const SizedBox(height: 4),
                            Text(g['desc'] as String, style: const TextStyle(fontSize: 13, color: Colors.black54), maxLines: 2, overflow: TextOverflow.ellipsis),
                          ],
                        ),
                      ),
                      const Spacer(),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text('${g['point']} điểm', style: const TextStyle(color: Colors.green, fontWeight: FontWeight.bold)),
                            Text('Còn: ${g['remain']}', style: const TextStyle(fontSize: 12, color: Colors.black54)),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: ElevatedButton(
                          onPressed: () {},
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF10B981),
                            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                            minimumSize: const Size.fromHeight(36),
                          ),
                          child: const Text('Đổi quà'),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
            const SizedBox(height: 24),
            const Text('Lịch sử đổi quà gần đây', style: TextStyle(fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            if (giftHistory.isEmpty)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(vertical: 32),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  children: const [
                    Icon(Icons.card_giftcard, color: Colors.black26, size: 36),
                    SizedBox(height: 8),
                    Text('Chưa có lịch sử đổi quà', style: TextStyle(color: Colors.black54)),
                  ],
                ),
              ),
            const Divider(height: 32),
            AccountMenuList(
              activeIndex: 3,
              onMenuTap: (context, index) {
                switch (index) {
                  case 0:
                    Navigator.of(context).pushReplacement(
                      MaterialPageRoute(builder: (context) => const ProfileScreen()),
                    );
                    break;
                  case 1:
                    Navigator.of(context).pushReplacement(
                      MaterialPageRoute(builder: (context) => const StatisticScreen()),
                    );
                    break;
                  case 2:
                    Navigator.of(context).pushReplacement(
                      MaterialPageRoute(builder: (context) => const EventMenuScreen()),
                    );
                    break;
                  case 3:
                    // Đã ở Quà tặng
                    break;
                  case 4:
                    Navigator.of(context).pushNamedAndRemoveUntil('/', (route) => false);
                    break;
                }
              },
            ),
          ],
        ),
      ),
    );
  }
}

Widget _giftStep(String step, String title, String desc) {
  return Expanded(
    child: Column(
      children: [
        CircleAvatar(
          radius: 18,
          backgroundColor: Colors.white,
          child: Text(step, style: const TextStyle(fontWeight: FontWeight.bold, color: Color(0xFF10B981))),
        ),
        const SizedBox(height: 4),
        Text(title, style: const TextStyle(fontWeight: FontWeight.bold, color: Color(0xFF10B981))),
        const SizedBox(height: 2),
        Text(desc, style: const TextStyle(fontSize: 12, color: Colors.black54), textAlign: TextAlign.center),
      ],
    ),
  );
} 