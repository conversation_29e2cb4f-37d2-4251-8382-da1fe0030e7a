<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Cache;

class Translation extends Model
{
    protected $fillable = ['key', 'locale', 'value'];

    protected static function boot()
    {
        parent::boot();

        // Clear specific cache when translation is saved or deleted
        static::saved(function ($translation) {
            self::clearTranslationCache($translation->locale, $translation->key);
        });

        static::deleted(function ($translation) {
            self::clearTranslationCache($translation->locale, $translation->key);
        });
    }

    /**
     * Clear translation cache efficiently
     */
    private static function clearTranslationCache(string $locale, string $key): void
    {
        $cacheKeys = [
            "trans_{$locale}_{$key}",
            "translations_{$locale}",
            "current_language_{$locale}",
            "translation_progress_{$locale}",
        ];

        foreach ($cacheKeys as $cacheKey) {
            Cache::forget($cacheKey);
        }
    }

    /**
     * Get the language that owns this translation
     */
    public function language(): BelongsTo
    {
        return $this->belongsTo(Language::class, 'locale', 'locale');
    }

    /**
     * Get translation value with fallback (optimized)
     */
    public static function get(string $key, string $locale = null, string $fallback = null): string
    {
        $locale = $locale ?? app()->getLocale();
        $fallback = $fallback ?? $key;

        return Cache::remember("trans_{$locale}_{$key}", 3600, function () use ($key, $locale, $fallback) {
            // Single query to get both current and fallback translations
            $translations = static::whereIn('locale', [$locale, Language::getDefaultLocale()])
                ->where('key', $key)
                ->pluck('value', 'locale');

            // Return current locale translation if exists
            if (isset($translations[$locale])) {
                return $translations[$locale];
            }

            // Return fallback locale translation if exists
            $fallbackLocale = Language::getDefaultLocale();
            if ($locale !== $fallbackLocale && isset($translations[$fallbackLocale])) {
                return $translations[$fallbackLocale];
            }

            return $fallback;
        });
    }

    /**
     * Set translation value
     */
    public static function set(string $key, string $value, string $locale = null): Translation
    {
        $locale = $locale ?? app()->getLocale();

        return static::updateOrCreate(
            ['key' => $key, 'locale' => $locale],
            ['value' => $value]
        );
    }

    /**
     * Get all translations for a locale
     */
    public static function getForLocale(string $locale): array
    {
        return Cache::rememberForever("translations_{$locale}", function () use ($locale) {
            return static::where('locale', $locale)
                ->pluck('value', 'key')
                ->toArray();
        });
    }

    /**
     * Get missing translations for a locale
     */
    public static function getMissingForLocale(string $locale): array
    {
        $allKeys = static::distinct()->pluck('key')->toArray();
        $existingKeys = static::where('locale', $locale)->pluck('key')->toArray();

        return array_diff($allKeys, $existingKeys);
    }

    /**
     * Import translations from array (optimized with bulk operations)
     */
    public static function importFromArray(array $translations, string $locale): int
    {
        if (empty($translations)) {
            return 0;
        }

        $imported = 0;
        $batchSize = 100;
        $chunks = array_chunk($translations, $batchSize, true);

        foreach ($chunks as $chunk) {
            $upsertData = [];
            foreach ($chunk as $key => $value) {
                if (is_string($value)) {
                    $upsertData[] = [
                        'key' => $key,
                        'locale' => $locale,
                        'value' => $value,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];
                    $imported++;
                }
            }

            if (!empty($upsertData)) {
                static::upsert($upsertData, ['key', 'locale'], ['value', 'updated_at']);
            }
        }

        // Clear specific cache
        self::clearTranslationCache($locale, '');

        return $imported;
    }

    /**
     * Export translations to array
     */
    public static function exportToArray(string $locale = null): array
    {
        $query = static::query();

        if ($locale) {
            $query->where('locale', $locale);
        }

        return $query->pluck('value', 'key')->toArray();
    }
}
