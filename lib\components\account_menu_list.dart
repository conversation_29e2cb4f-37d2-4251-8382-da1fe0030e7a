import 'package:flutter/material.dart';
import '../constants/colors.dart';

class AccountMenuList extends StatelessWidget {
  final void Function(BuildContext context, int index)? onMenuTap;
  final int activeIndex;
  const AccountMenuList({super.key, this.onMenuTap, required this.activeIndex});

  @override
  Widget build(BuildContext context) {
    final items = [
      {
        'icon': Icons.account_circle_outlined,
        'color': AppColors.primary,
        'bg': AppColors.surface,
        'label': 'Trang cá nhân',
      },
      {
        'icon': Icons.bar_chart,
        'color': AppColors.primary,
        'bg': AppColors.surface,
        'label': 'Thống kê',
      },
      {
        'icon': Icons.event,
        'color': AppColors.primary,
        'bg': AppColors.surface,
        'label': 'Sự kiện',
      },
      {
        'icon': Icons.card_giftcard,
        'color': AppColors.primary,
        'bg': AppColors.surface,
        'label': 'Quà tặng',
      },
      {
        'icon': Icons.logout,
        'color': AppColors.error,
        'bg': AppColors.surface,
        'label': 'Đăng xuất',
      },
    ];
    return Column(
      children: List.generate(items.length, (i) {
        final item = items[i];
        final bool isActive = i == activeIndex;
        return ListTile(
          leading: Icon(item['icon'] as IconData, color: item['color'] as Color),
          title: Text(
            item['label'] as String,
            style: TextStyle(
              color: i == 4 ? AppColors.error : AppColors.textDark,
              fontFamily: 'Roboto',
              fontWeight: FontWeight.w500,
            ),
          ),
          tileColor: isActive ? item['bg'] as Color : null,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
          onTap: isActive ? null : () => onMenuTap?.call(context, i),
        );
      }),
    );
  }
} 