import 'package:flutter/material.dart';
import '../../components/account_menu_list.dart';
import 'profile_screen.dart';
import 'statistic_screen.dart';
import 'gift_screen.dart';

class EventMenuScreen extends StatelessWidget {
  const EventMenuScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final events = [
      {
        'name': 'Nihil veniam in quod.',
        'time': '10/07/2025 02:34',
        'address': '79550 O\'Conner Mountain Apt. 306\nPort Melissa, SD 58790-6559',
        'status': 'Chờ duyệt',
        'created': '01/07/2025 21:52',
      },
      {
        'name': 'Iure est ut corrupti.',
        'time': '14/07/2025 11:10',
        'address': '9673 Gleichner View Suite 378 Port Monteburgh, OR 97855',
        'status': 'Chờ duyệt',
        'created': '01/07/2025 21:52',
      },
      {
        'name': 'At in sed magnam quibusdam vero.',
        'time': '14/07/2025 19:18',
        'address': '459 Cruickshank Pine Apt. 192\nGiovanifort, MS 02021',
        'status': 'Ch<PERSON> duyệt',
        'created': '01/07/2025 21:52',
      },
    ];
    final joinedEvents = [];

    return Scaffold(
      appBar: AppBar(
        title: const Text('Danh sách sự kiện'),
        centerTitle: true,
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 12),
            child: ElevatedButton.icon(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                elevation: 0,
              ),
              icon: const Icon(Icons.add, size: 20),
              label: const Text('Tạo sự kiện mới', style: TextStyle(fontWeight: FontWeight.bold)),
              onPressed: () {},
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Sự kiện do bạn tạo', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
            const SizedBox(height: 10),
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[200]!),
              ),
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: DataTable(
                  headingRowColor: MaterialStateProperty.all(const Color(0xFFF3F4F6)),
                  columns: const [
                    DataColumn(label: Text('Tên sự kiện', style: TextStyle(fontWeight: FontWeight.bold))),
                    DataColumn(label: Text('Thời gian', style: TextStyle(fontWeight: FontWeight.bold))),
                    DataColumn(label: Text('Địa điểm', style: TextStyle(fontWeight: FontWeight.bold))),
                    DataColumn(label: Text('Trạng thái', style: TextStyle(fontWeight: FontWeight.bold))),
                    DataColumn(label: Text('Ngày tạo', style: TextStyle(fontWeight: FontWeight.bold))),
                  ],
                  rows: events.map((e) => DataRow(
                    cells: [
                      DataCell(
                        InkWell(
                          child: Text(
                            e['name'] as String,
                            style: const TextStyle(color: Color(0xFF1E40AF), fontWeight: FontWeight.w500, decoration: TextDecoration.underline),
                          ),
                          onTap: () {},
                        ),
                      ),
                      DataCell(Text(e['time'] as String)),
                      DataCell(Text(e['address'] as String)),
                      DataCell(Text(e['status'] as String, style: const TextStyle(color: Color(0xFFF59E42), fontWeight: FontWeight.bold))),
                      DataCell(Text(e['created'] as String)),
                    ],
                  )).toList(),
                ),
              ),
            ),
            const SizedBox(height: 28),
            const Text('Sự kiện bạn đã tham gia', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
            const SizedBox(height: 8),
            if (joinedEvents.isEmpty)
              const Text('Bạn chưa tham gia sự kiện nào.', style: TextStyle(color: Colors.black54)),
            const Divider(height: 32),
            AccountMenuList(
              activeIndex: 2,
              onMenuTap: (context, index) {
                switch (index) {
                  case 0:
                    Navigator.of(context).pushReplacement(
                      MaterialPageRoute(builder: (context) => const ProfileScreen()),
                    );
                    break;
                  case 1:
                    Navigator.of(context).pushReplacement(
                      MaterialPageRoute(builder: (context) => const StatisticScreen()),
                    );
                    break;
                  case 2:
                    // Đã ở Sự kiện
                    break;
                  case 3:
                    Navigator.of(context).pushReplacement(
                      MaterialPageRoute(builder: (context) => const GiftScreen()),
                    );
                    break;
                  case 4:
                    Navigator.of(context).pushNamedAndRemoveUntil('/', (route) => false);
                    break;
                }
              },
            ),
          ],
        ),
      ),
    );
  }
} 