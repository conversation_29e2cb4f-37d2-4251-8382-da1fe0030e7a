<?php

namespace App\Http\Middleware;

use App\Models\Language;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class EnsureLocalePrefix
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function handle(Request $request, Closure $next): Response
    {
        $path = $request->path();
        
        // Skip if already has locale prefix or is admin/api route
        if (preg_match('/^[a-z]{2}\//', $path) || 
            str_starts_with($path, 'admin') || 
            str_starts_with($path, 'api') ||
            $path === 'set-locale' ||
            str_starts_with($path, '_debugbar')) {
            return $next($request);
        }
        
        // Get default locale
        $defaultLocale = Language::getDefaultLocale();
        
        // Redirect to URL with locale prefix
        $newUrl = url("/{$defaultLocale}/" . ltrim($path, '/'));
        
        return redirect($newUrl, 301);
    }
}
