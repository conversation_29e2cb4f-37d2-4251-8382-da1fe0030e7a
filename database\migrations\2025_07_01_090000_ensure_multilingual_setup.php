<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Ensure languages table exists with correct structure
        if (!Schema::hasTable('languages')) {
            Schema::create('languages', function (Blueprint $table) {
                $table->id();
                $table->string('locale', 5)->unique();
                $table->string('name', 100);
                $table->boolean('is_default')->default(false);
                $table->boolean('is_active')->default(true);
                $table->timestamps();
            });
        } else {
            // Add is_active column if it doesn't exist
            if (!Schema::hasColumn('languages', 'is_active')) {
                Schema::table('languages', function (Blueprint $table) {
                    $table->boolean('is_active')->default(true)->after('is_default');
                });
            }
        }

        // Ensure translations table exists with correct structure
        if (!Schema::hasTable('translations')) {
            Schema::create('translations', function (Blueprint $table) {
                $table->id();
                $table->string('key');
                $table->string('locale', 5);
                $table->text('value');
                $table->timestamps();
                
                $table->unique(['key', 'locale']);
                $table->index(['locale']);
                $table->index(['key']);
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Don't drop tables in production
        // Schema::dropIfExists('translations');
        // Schema::dropIfExists('languages');
    }
};
