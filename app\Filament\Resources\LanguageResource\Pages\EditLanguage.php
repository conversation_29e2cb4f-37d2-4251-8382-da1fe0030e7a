<?php

namespace App\Filament\Resources\LanguageResource\Pages;

use App\Filament\Resources\LanguageResource;
use App\Models\Language;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Support\Facades\Cache;

class EditLanguage extends EditRecord
{
    protected static string $resource = LanguageResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make()
                ->before(function () {
                    if ($this->record->is_default) {
                        $this->halt();
                        return false;
                    }
                    return true;
                }),
        ];
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        // If this is set as default, make sure no other language is default
        if ($data['is_default'] ?? false) {
            Language::where('id', '!=', $this->record->id)->update(['is_default' => false]);
            $data['is_active'] = true; // Default language must be active
        }

        return $data;
    }

    protected function afterSave(): void
    {
        // Clear cache after updating language
        Cache::forget('default_locale');
        Cache::forget('available_locales');
    }
}
