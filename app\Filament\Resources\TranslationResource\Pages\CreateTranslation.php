<?php

namespace App\Filament\Resources\TranslationResource\Pages;

use App\Filament\Resources\TranslationResource;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Facades\Cache;

class CreateTranslation extends CreateRecord
{
    protected static string $resource = TranslationResource::class;

    protected function afterCreate(): void
    {
        // Clear translation cache
        Cache::flush();

        // Regenerate translation files
        $this->regenerateTranslationFiles();

        // Show success notification
        \Filament\Notifications\Notification::make()
            ->title('Translation Created')
            ->body('New translation has been created and cache has been cleared.')
            ->success()
            ->send();
    }

    protected function regenerateTranslationFiles(): void
    {
        try {
            // Call the export command to regenerate translation files
            \Illuminate\Support\Facades\Artisan::call('translations:export-all');

            // Also update the JSON files
            $this->updateJsonTranslations();

        } catch (\Exception $e) {
            \Log::error('Failed to regenerate translation files: ' . $e->getMessage());
        }
    }

    protected function updateJsonTranslations(): void
    {
        // Update all_translations.json file
        $translations = \App\Models\Translation::all()->groupBy('locale');
        $allTranslations = [];

        foreach ($translations as $locale => $localeTranslations) {
            $allTranslations[$locale] = [];
            foreach ($localeTranslations as $translation) {
                $allTranslations[$locale][$translation->key] = $translation->value;
            }
        }

        // Save to all_translations.json
        $filePath = storage_path('app/translations/all_translations.json');
        if (!file_exists(dirname($filePath))) {
            mkdir(dirname($filePath), 0755, true);
        }

        file_put_contents($filePath, json_encode($allTranslations, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    }
}
