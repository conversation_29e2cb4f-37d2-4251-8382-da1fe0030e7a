import 'package:flutter/material.dart';

class EventScreen extends StatefulWidget {
  const EventScreen({super.key});

  @override
  State<EventScreen> createState() => _EventScreenState();
}

class _EventScreenState extends State<EventScreen> {
  final List<Map<String, dynamic>> events = [
    {
      'name': 'Sự kiện Môi trường Xanh',
      'time': '10/07/2025 08:00',
      'status': 'Sắp diễn ra',
      'description': '<PERSON><PERSON>ng nhau làm sạch công viên thành phố.',
      'joined': false,
    },
    {
      'name': '<PERSON><PERSON><PERSON> hộ<PERSON>ái chế',
      'time': '12/07/2025 09:00',
      'status': 'Đang diễn ra',
      'description': 'Mang đồ tái chế đến điểm tập kết và nhận quà.',
      'joined': true,
    },
    {
      'name': 'Trồng cây gây rừng',
      'time': '20/07/2025 07:30',
      'status': 'Đ<PERSON> kết thúc',
      'description': 'Tham gia trồng cây tại khu vực ngoại ô.',
      'joined': false,
    },
  ];

  void openEventDetail(BuildContext context, Map<String, dynamic> event) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (_) => EventDetailScreen(event: event),
      ),
    );
  }

  void showQRScanner(BuildContext context) {
    showDialog(
      context: context,
      builder: (ctx) => AlertDialog(
        title: const Text('Quét mã QR'),
        content: const SizedBox(
          height: 120,
          child: Center(child: Icon(Icons.qr_code_scanner, size: 64, color: Colors.green)),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(ctx).pop(),
            child: const Text('Đóng'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Danh sách sự kiện'),
        centerTitle: true,
      ),
      body: ListView.separated(
        padding: const EdgeInsets.all(16),
        itemCount: events.length,
        separatorBuilder: (_, __) => const SizedBox(height: 16),
        itemBuilder: (context, index) {
          final event = events[index];
          final status = event['status'] as String;
          final joined = event['joined'] as bool;
          return InkWell(
            onTap: () => openEventDetail(context, event),
            borderRadius: BorderRadius.circular(12),
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.08),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text(
                          event['name'] as String,
                          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                        decoration: BoxDecoration(
                          color: status == 'Đang diễn ra'
                              ? Colors.green.withOpacity(0.15)
                              : status == 'Sắp diễn ra'
                                  ? Colors.blue.withOpacity(0.15)
                                  : Colors.grey.withOpacity(0.15),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          status,
                          style: TextStyle(
                            color: status == 'Đang diễn ra'
                                ? Colors.green
                                : status == 'Sắp diễn ra'
                                    ? Colors.blue
                                    : Colors.grey,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(event['time'] as String, style: const TextStyle(color: Colors.black54)),
                  const SizedBox(height: 8),
                  Text(event['description'] as String, maxLines: 2, overflow: TextOverflow.ellipsis),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      if (!joined)
                        ElevatedButton(
                          onPressed: () {
                            setState(() {
                              events[index]['joined'] = true;
                            });
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue,
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                          ),
                          child: const Text('Tham gia'),
                        ),
                      if (joined)
                        ElevatedButton(
                          onPressed: null,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.grey,
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                          ),
                          child: const Text('Đã tham gia'),
                        ),
                      if (joined && status == 'Đang diễn ra') ...[
                        const SizedBox(width: 12),
                        ElevatedButton.icon(
                          onPressed: () => showQRScanner(context),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                          ),
                          icon: const Icon(Icons.qr_code_scanner, size: 18),
                          label: const Text('Báo danh'),
                        ),
                      ],
                    ],
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}

class EventDetailScreen extends StatelessWidget {
  final Map<String, dynamic> event;
  const EventDetailScreen({super.key, required this.event});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(event['name'] ?? 'Chi tiết sự kiện'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(event['name'] ?? '', style: const TextStyle(fontSize: 22, fontWeight: FontWeight.bold)),
            const SizedBox(height: 12),
            Text('Thời gian: ${event['time'] ?? ''}', style: const TextStyle(fontSize: 16)),
            const SizedBox(height: 8),
            Text('Trạng thái: ${event['status'] ?? ''}', style: const TextStyle(fontSize: 16)),
            const SizedBox(height: 16),
            Text(event['description'] ?? '', style: const TextStyle(fontSize: 15)),
          ],
        ),
      ),
    );
  }
} 