# EcoSolves - Flutter App

Ứng dụng Flutter đa nền tảng với các tính năng báo c<PERSON>o, bản đồ cộng đồng, s<PERSON> kiệ<PERSON>, diễn đàn và hệ thống xác thực hiện đại.

## Tính năng chính

### 🎨 Màn hình Splash
- Animation logo với hiệu ứng scale và rotate
- Gradient background đẹp mắt
- Tự động chuyển sang màn hình đăng nhập sau 3 giây
- Loading indicator

### 🔐 Màn hình Authentication
- **Đăng nhập**: Form validation, hiển thị/ẩn mật khẩu, liên kết đăng ký/quên mật khẩu
- **Đăng ký**: Form validation, xác nhận mật khẩu, đồng ý điều khoản
- **Quên mật khẩu**: G<PERSON>i email đặt lại mật khẩu, trạng thái thành công/thất bại

### 🏠 <PERSON><PERSON><PERSON> hình Home
- Bottom navigation bar hiện đại: Home, <PERSON><PERSON><PERSON> đồ, <PERSON><PERSON><PERSON> c<PERSON> (nút nổi), Sự kiện, Diễn đàn
- Chuyển tab mượt mà, giao diện đẹp

### 🗺️ Màn hình Bản đồ
- Sử dụng OpenStreetMap (qua flutter_map) miễn phí, không cần API key
- Lấy danh sách điểm rác từ API động
- Hiển thị marker cho từng điểm rác
- Nhấn vào marker hiển thị chi tiết: mô tả, ảnh, ngày tạo, link Google Maps

### 📝 Màn hình Báo cáo
- Form báo cáo điểm rác mới: upload ảnh, mô tả, GPS, link Google Maps
- Lấy vị trí GPS tự động hoặc nhập tay
- Gửi báo cáo lên server (có validate)

### 📅 Màn hình Sự kiện
- Placeholder, sẵn sàng mở rộng cho các sự kiện cộng đồng

### 💬 Màn hình Diễn đàn
- Placeholder, sẵn sàng mở rộng cho thảo luận cộng đồng

## Cấu trúc thư mục

```
lib/
├── main.dart
└── screens/
    ├── splash_screen.dart
    ├── gradient_button.dart
    ├── auth/
    │   ├── login_screen.dart
    │   ├── register_screen.dart
    │   └── forgot_password_screen.dart
    └── home/
        ├── home_screen.dart
        ├── report_screen.dart
        ├── map_screen.dart
        ├── event_screen.dart
        └── forum_screen.dart
```

## Cài đặt và chạy

1. Đảm bảo đã cài đặt Flutter SDK
2. Clone dự án
3. Chạy lệnh:
```bash
flutter pub get
flutter run
```

## Công nghệ sử dụng
- **Flutter 3.x**
- **OpenStreetMap** qua flutter_map
- **HTTP API** (báo cáo, bản đồ, xác thực)
- **Material Design 3**
- **image_picker** (chọn ảnh)

## Tính năng sắp tới
- [ ] Tích hợp Firebase Authentication
- [ ] Lưu trữ local với SharedPreferences
- [ ] Quản lý state với Provider/Bloc
- [ ] Tích hợp Google/Facebook login thực tế
- [ ] Màn hình đặt lại mật khẩu mới
- [ ] Quản lý báo cáo, phê duyệt, phản hồi

## Đóng góp
Mọi đóng góp đều được chào đón! Vui lòng tạo issue hoặc pull request.

<EMAIL>