import 'package:flutter/material.dart';
import '../../components/account_menu_list.dart';
import 'statistic_screen.dart';
import 'event_menu_screen.dart';
import 'gift_screen.dart';

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final user = {
      'name': 'thuc',
      'username': 'acmatvjrus',
      'email': '<EMAIL>',
      'phone': '',
      'address': '',
      'point': 5,
      'joinDate': '01/07/2025',
      'status': 'Hoạt động',
    };

    return Scaffold(
      appBar: AppBar(
        title: const Text('Trang cá nhân'),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.edit, color: Color(0xFF0EA5E9)),
            tooltip: 'Chỉnh sửa thông tin',
            onPressed: () {
              showDialog(
                context: context,
                builder: (context) => AlertDialog(
                  title: const Text('Chỉnh sửa thông tin'),
                  content: const Text('Chức năng đang phát triển!'),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('Đóng'),
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Avatar + info
                Column(
                  children: [
                    CircleAvatar(
                      radius: 40,
                      backgroundColor: Colors.grey[300],
                      child: Text(
                        (user['name'] as String).substring(0, 2).toUpperCase(),
                        style: const TextStyle(fontSize: 32, fontWeight: FontWeight.bold, color: Colors.black54),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      user['name'] as String,
                      style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 20),
                    ),
                    Text(
                      user['username'] as String,
                      style: const TextStyle(color: Colors.black54, fontSize: 15),
                    ),
                    Text(
                      user['email'] as String,
                      style: const TextStyle(color: Colors.black54, fontSize: 15),
                    ),
                  ],
                ),
                const SizedBox(width: 32),
                // Thông tin bên phải
                Expanded(
                  child: Column(
                    children: [
                      _infoField('Số điện thoại:', user['phone'] as String),
                      _infoField('Địa chỉ:', user['address'] as String),
                      _infoField('Điểm tích lũy:', '${user['point']} điểm', valueColor: Colors.green, isBold: true),
                      _infoField('Ngày tham gia:', user['joinDate'] as String),
                      _infoField(
                        'Trạng thái:',
                        user['status'] as String,
                        valueWidget: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.green[50],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Text('Hoạt động', style: TextStyle(color: Colors.green, fontWeight: FontWeight.bold)),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 32),
            const Divider(height: 32),
            AccountMenuList(
              activeIndex: 0,
              onMenuTap: (context, index) {
                switch (index) {
                  case 0:
                    // Đã ở Trang cá nhân
                    break;
                  case 1:
                    Navigator.of(context).pushReplacement(
                      MaterialPageRoute(builder: (context) => const StatisticScreen()),
                    );
                    break;
                  case 2:
                    Navigator.of(context).pushReplacement(
                      MaterialPageRoute(builder: (context) => const EventMenuScreen()),
                    );
                    break;
                  case 3:
                    Navigator.of(context).pushReplacement(
                      MaterialPageRoute(builder: (context) => const GiftScreen()),
                    );
                    break;
                  case 4:
                    Navigator.of(context).pushNamedAndRemoveUntil('/', (route) => false);
                    break;
                }
              },
            ),
          ],
        ),
      ),
    );
  }
}

Widget _infoField(String label, String value, {Color? valueColor, bool isBold = false, Widget? valueWidget}) {
  return Container(
    margin: const EdgeInsets.only(bottom: 12),
    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(8),
      border: Border.all(color: Colors.grey[200]!),
    ),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(label, style: const TextStyle(fontWeight: FontWeight.w500)),
        valueWidget ?? Text(
          value,
          style: TextStyle(
            color: valueColor,
            fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ],
    ),
  );
} 