<?php

namespace App\Filament\Resources;

use App\Filament\Resources\LanguageResource\Pages;
use App\Models\Language;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Cache;
use Filament\Notifications\Notification;

class LanguageResource extends Resource
{
    protected static ?string $model = Language::class;

    protected static ?string $navigationIcon = 'heroicon-o-language';

    protected static ?string $navigationGroup = 'Localization';

    protected static ?string $navigationLabel = 'Languages';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('locale')
                    ->label('Locale Code')
                    ->required()
                    ->maxLength(5)
                    ->placeholder('en, vi, fr...')
                    ->regex('/^[a-z]{2}(_[A-Z]{2})?$/')
                    ->helperText('Format: en, vi, en_US, fr_FR')
                    ->unique(ignoreRecord: true),

                Forms\Components\TextInput::make('name')
                    ->label('Language Name')
                    ->required()
                    ->maxLength(100)
                    ->placeholder('English, Vietnamese, French...'),

                Forms\Components\Toggle::make('is_active')
                    ->label('Active')
                    ->default(true)
                    ->helperText('Enable/disable this language'),

                Forms\Components\Toggle::make('is_default')
                    ->label('Default Language')
                    ->default(false)
                    ->helperText('Set as default language for the site')
                    ->reactive()
                    ->afterStateUpdated(function ($state, callable $set) {
                        if ($state) {
                            // If this is set as default, ensure it's also active
                            $set('is_active', true);
                        }
                    }),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('locale')
                    ->label('Locale')
                    ->searchable()
                    ->sortable()
                    ->badge()
                    ->color('primary'),

                Tables\Columns\TextColumn::make('name')
                    ->label('Language Name')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('Active')
                    ->boolean()
                    ->sortable(),

                Tables\Columns\IconColumn::make('is_default')
                    ->label('Default')
                    ->boolean()
                    ->sortable(),

                Tables\Columns\TextColumn::make('translations_count')
                    ->label('Translations')
                    ->counts('translations')
                    ->sortable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Active Status'),
                Tables\Filters\TernaryFilter::make('is_default')
                    ->label('Default Language'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->before(function (Language $record) {
                        if ($record->is_default) {
                            Notification::make()
                                ->title('Cannot delete default language')
                                ->danger()
                                ->send();
                            return false;
                        }
                        return true;
                    }),
                Tables\Actions\Action::make('set_default')
                    ->label('Set Default')
                    ->icon('heroicon-o-star')
                    ->color('warning')
                    ->visible(fn (Language $record) => !$record->is_default)
                    ->action(function (Language $record) {
                        // Remove default from all languages
                        Language::query()->update(['is_default' => false]);
                        
                        // Set this language as default and active
                        $record->update([
                            'is_default' => true,
                            'is_active' => true
                        ]);

                        // Clear cache
                        Cache::forget('default_locale');
                        Cache::forget('available_locales');

                        Notification::make()
                            ->title('Default language updated')
                            ->success()
                            ->send();
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->before(function ($records) {
                            $defaultLanguage = $records->where('is_default', true)->first();
                            if ($defaultLanguage) {
                                Notification::make()
                                    ->title('Cannot delete default language')
                                    ->danger()
                                    ->send();
                                return false;
                            }
                            return true;
                        }),
                    Tables\Actions\BulkAction::make('activate')
                        ->label('Activate Selected')
                        ->icon('heroicon-o-check')
                        ->color('success')
                        ->action(function ($records) {
                            $records->each->update(['is_active' => true]);
                            Cache::forget('available_locales');
                            
                            Notification::make()
                                ->title('Languages activated')
                                ->success()
                                ->send();
                        }),
                    Tables\Actions\BulkAction::make('deactivate')
                        ->label('Deactivate Selected')
                        ->icon('heroicon-o-x-mark')
                        ->color('danger')
                        ->action(function ($records) {
                            // Don't deactivate default language
                            $records->where('is_default', false)->each->update(['is_active' => false]);
                            Cache::forget('available_locales');
                            
                            Notification::make()
                                ->title('Languages deactivated')
                                ->success()
                                ->send();
                        }),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListLanguages::route('/'),
            'create' => Pages\CreateLanguage::route('/create'),
            'edit' => Pages\EditLanguage::route('/{record}/edit'),
        ];
    }
}
