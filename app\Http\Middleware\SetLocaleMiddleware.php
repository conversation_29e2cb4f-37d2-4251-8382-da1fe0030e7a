<?php

namespace App\Http\Middleware;

use App\Models\Language;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;
use Symfony\Component\HttpFoundation\Response;

class SetLocaleMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param \Closure(Request): (Response) $next
     * @return Response
     */
    public function handle(Request $request, Closure $next): Response
    {
        $urlLocale = $request->segment(1);
        $availableLocales = Language::getAvailableLocales();

        // Priority 1: URL locale (most important)
        if (in_array($urlLocale, $availableLocales)) {
            App::setLocale($urlLocale);
            Session::put('locale', $urlLocale);
        }
        // Priority 2: Session locale
        elseif (Session::has('locale') && in_array(Session::get('locale'), $availableLocales)) {
            $sessionLocale = Session::get('locale');
            App::setLocale($sessionLocale);
        }
        // Priority 3: Auto-detect
        else {
            $detectedLocale = $this->detectPreferredLocale($request, $availableLocales);
            App::setLocale($detectedLocale);
            Session::put('locale', $detectedLocale);
        }

        return $next($request);
    }

    /**
     * Detect user's preferred locale from Accept-Language header
     */
    protected function detectPreferredLocale(Request $request, array $availableLocales): string
    {
        $acceptLanguage = $request->header('Accept-Language');

        if (!$acceptLanguage) {
            return Language::getDefaultLocale();
        }

        // Parse Accept-Language header
        $preferredLanguages = $this->parseAcceptLanguage($acceptLanguage);

        // Find the best match
        foreach ($preferredLanguages as $lang) {
            // Check exact match first (e.g., en-US)
            if (in_array($lang['locale'], $availableLocales)) {
                return $lang['locale'];
            }

            // Check language part only (e.g., en from en-US)
            $languageOnly = substr($lang['locale'], 0, 2);
            if (in_array($languageOnly, $availableLocales)) {
                return $languageOnly;
            }
        }

        // No match found, return default
        return Language::getDefaultLocale();
    }

    /**
     * Parse Accept-Language header into array of locales with quality values
     */
    protected function parseAcceptLanguage(string $acceptLanguage): array
    {
        $languages = [];
        $parts = explode(',', $acceptLanguage);

        foreach ($parts as $part) {
            $part = trim($part);

            if (strpos($part, ';q=') !== false) {
                [$locale, $quality] = explode(';q=', $part, 2);
                $quality = (float) $quality;
            } else {
                $locale = $part;
                $quality = 1.0;
            }

            $locale = trim($locale);
            $locale = str_replace('_', '-', $locale); // Normalize format

            // Convert to our format (e.g., en-US -> en)
            if (strpos($locale, '-') !== false) {
                $localeParts = explode('-', $locale);
                $mainLocale = strtolower($localeParts[0]);
                $languages[] = [
                    'locale' => $locale,
                    'quality' => $quality
                ];
                $languages[] = [
                    'locale' => $mainLocale,
                    'quality' => $quality - 0.1 // Slightly lower priority for language-only
                ];
            } else {
                $languages[] = [
                    'locale' => strtolower($locale),
                    'quality' => $quality
                ];
            }
        }

        // Sort by quality (highest first)
        usort($languages, function ($a, $b) {
            return $b['quality'] <=> $a['quality'];
        });

        return $languages;
    }
}
