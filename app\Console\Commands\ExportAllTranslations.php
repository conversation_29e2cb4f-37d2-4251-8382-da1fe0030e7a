<?php

namespace App\Console\Commands;

use App\Models\Language;
use App\Models\Translation;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class ExportAllTranslations extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'translations:export-all 
                            {--output-dir=storage/app/translations : Output directory}';

    /**
     * The console command description.
     */
    protected $description = 'Export all translations to JSON files for admin import/export';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $outputDir = $this->option('output-dir');

        // Create output directory if it doesn't exist
        if (!File::exists($outputDir)) {
            File::makeDirectory($outputDir, 0755, true);
        }

        $this->info("Exporting all translations to: {$outputDir}");

        $languages = Language::where('is_active', true)->get();
        $allTranslations = [];
        $allKeys = Translation::distinct()->pluck('key')->toArray();

        // Export individual language files
        foreach ($languages as $language) {
            $translations = Translation::where('locale', $language->locale)
                ->orderBy('key')
                ->pluck('value', 'key')
                ->toArray();

            $filename = "{$language->locale}.json";
            $filepath = "{$outputDir}/{$filename}";
            
            File::put($filepath, json_encode($translations, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
            
            $this->line("Generated: {$filename} (" . count($translations) . " translations)");
        }

        // Build combined structure: key => [locale => value]
        foreach ($allKeys as $key) {
            $allTranslations[$key] = [];
            foreach ($languages as $language) {
                $translation = Translation::where('key', $key)
                    ->where('locale', $language->locale)
                    ->value('value');
                $allTranslations[$key][$language->locale] = $translation ?? '';
            }
        }

        // Export combined file
        $combinedData = [
            'meta' => [
                'generated_at' => now()->toISOString(),
                'locales' => $languages->pluck('locale')->toArray(),
                'total_keys' => count($allTranslations),
                'languages' => $languages->map(function($lang) {
                    return [
                        'locale' => $lang->locale,
                        'name' => $lang->name,
                        'is_default' => $lang->is_default,
                        'is_active' => $lang->is_active,
                    ];
                })->toArray(),
            ],
            'translations' => $allTranslations,
        ];

        $combinedFilepath = "{$outputDir}/all_translations.json";
        File::put($combinedFilepath, json_encode($combinedData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        
        $this->line("Generated: all_translations.json (" . count($allTranslations) . " keys)");

        // Export flat structure for easy import
        $flatStructure = [];
        foreach ($allTranslations as $key => $localeValues) {
            foreach ($localeValues as $locale => $value) {
                $flatStructure[] = [
                    'key' => $key,
                    'locale' => $locale,
                    'value' => $value,
                ];
            }
        }

        $flatFilepath = "{$outputDir}/translations_flat.json";
        File::put($flatFilepath, json_encode($flatStructure, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        
        $this->line("Generated: translations_flat.json (" . count($flatStructure) . " entries)");

        // Export CSV for easy editing
        $csvFilepath = "{$outputDir}/all_translations.csv";
        $handle = fopen($csvFilepath, 'w');
        
        // Header
        $header = array_merge(['Key'], $languages->pluck('locale')->toArray());
        fputcsv($handle, $header);
        
        // Data
        foreach ($allTranslations as $key => $localeValues) {
            $row = [$key];
            foreach ($languages as $language) {
                $row[] = $localeValues[$language->locale] ?? '';
            }
            fputcsv($handle, $row);
        }
        
        fclose($handle);
        $this->line("Generated: all_translations.csv");

        $this->info("Export completed successfully!");
        $this->info("Files generated in: {$outputDir}");
        
        return 0;
    }
}
