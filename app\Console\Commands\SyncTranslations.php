<?php

namespace App\Console\Commands;

use App\Models\Language;
use App\Models\Translation;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class SyncTranslations extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'translations:sync 
                            {direction=both : Direction to sync (db-to-files, files-to-db, both)}
                            {--locale= : Specific locale to sync}
                            {--force : Force overwrite existing translations}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync translations between database and lang files';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $direction = $this->argument('direction');
        $locale = $this->option('locale');
        $force = $this->option('force');

        $locales = $locale ? [$locale] : Language::where('is_active', true)->pluck('locale')->toArray();

        if (empty($locales)) {
            $this->error('No active languages found.');
            return 1;
        }

        $this->info("Syncing translations for locales: " . implode(', ', $locales));

        foreach ($locales as $currentLocale) {
            switch ($direction) {
                case 'db-to-files':
                    $this->syncDbToFiles($currentLocale, $force);
                    break;
                case 'files-to-db':
                    $this->syncFilesToDb($currentLocale, $force);
                    break;
                case 'both':
                    $this->syncFilesToDb($currentLocale, $force);
                    $this->syncDbToFiles($currentLocale, $force);
                    break;
                default:
                    $this->error('Invalid direction. Use: db-to-files, files-to-db, or both');
                    return 1;
            }
        }

        $this->info('Sync completed successfully!');
        return 0;
    }

    /**
     * Sync translations from database to lang files
     */
    protected function syncDbToFiles(string $locale, bool $force): void
    {
        $this->info("Syncing database to files for locale: {$locale}");

        $translations = Translation::where('locale', $locale)
            ->pluck('value', 'key')
            ->toArray();

        if (empty($translations)) {
            $this->warn("No translations found in database for locale: {$locale}");
            return;
        }

        $langPath = resource_path("lang/{$locale}.json");
        $existingTranslations = [];

        if (File::exists($langPath)) {
            $existingContent = File::get($langPath);
            $existingTranslations = json_decode($existingContent, true) ?? [];
        }

        $mergedTranslations = $force ? $translations : array_merge($existingTranslations, $translations);

        // Sort by key for consistency
        ksort($mergedTranslations);

        $jsonContent = json_encode($mergedTranslations, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);

        // Ensure directory exists
        $langDir = dirname($langPath);
        if (!File::exists($langDir)) {
            File::makeDirectory($langDir, 0755, true);
        }

        File::put($langPath, $jsonContent);

        $this->info("Updated {$langPath} with " . count($mergedTranslations) . " translations");
    }

    /**
     * Sync translations from lang files to database
     */
    protected function syncFilesToDb(string $locale, bool $force): void
    {
        $this->info("Syncing files to database for locale: {$locale}");

        $langPath = resource_path("lang/{$locale}.json");

        if (!File::exists($langPath)) {
            $this->warn("Lang file not found: {$langPath}");
            return;
        }

        $jsonContent = File::get($langPath);
        $translations = json_decode($jsonContent, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            $this->error("Invalid JSON in file: {$langPath}");
            return;
        }

        if (!is_array($translations)) {
            $this->error("JSON file must contain an object with key-value pairs: {$langPath}");
            return;
        }

        $imported = 0;
        $updated = 0;
        $skipped = 0;

        foreach ($translations as $key => $value) {
            if (!is_string($value)) {
                continue; // Skip non-string values
            }

            $existing = Translation::where('key', $key)
                ->where('locale', $locale)
                ->first();

            if ($existing) {
                if ($force || empty($existing->value)) {
                    $existing->update(['value' => $value]);
                    $updated++;
                } else {
                    $skipped++;
                }
            } else {
                Translation::create([
                    'key' => $key,
                    'locale' => $locale,
                    'value' => $value,
                ]);
                $imported++;
            }
        }

        $this->info("Processed {$langPath}: Imported: {$imported}, Updated: {$updated}, Skipped: {$skipped}");
    }
}
