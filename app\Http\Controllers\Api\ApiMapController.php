<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Report;

class ApiMapController extends Controller
{
    public function appMap(Request $request)
    {
        // Validate token
        if (!$this->validateToken($request)) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid token'
            ], 401);
        }

        $reports = Report::select([
            'id',
            'description',
            'image',
            'latitude',
            'longitude',
            'gmap_link',
            'status',
            'created_at'
        ])->orderByDesc('created_at')->get();

        return response()->json([
            'success' => true,
            'data' => $reports
        ]);
    }

    private function validateToken(Request $request)
    {
        $token = $request->header('token');
        return $token === '1Abcd@123';
    }
}
