import 'package:flutter/material.dart';

class AppColors {
  // Primary color palette
  static const Color background = Color(0xFFF0F5F9);
  static const Color surface = Color(0xFFC9D6DF);
  static const Color textSecondary = Color(0xFF52616B);
  static const Color textPrimary = Color(0xFF1E2022);
  
  // Additional colors for better contrast and functionality
  static const Color white = Colors.white;
  static const Color black = Colors.black;
  static const Color transparent = Colors.transparent;
  
  // Semantic colors
  static const Color primary = Color(0xFF52616B);
  static const Color secondary = Color(0xFFC9D6DF);
  static const Color accent = Color(0xFF1E2022);
  static const Color error = Color(0xFFE53E3E);
  static const Color success = Color(0xFF38A169);
  static const Color warning = Color(0xFFD69E2E);
  static const Color info = Color(0xFF3182CE);
  
  // Text colors
  static const Color textLight = Color(0xFFF0F5F9);
  static const Color textDark = Color(0xFF1E2022);
  static const Color textMedium = Color(0xFF52616B);
  
  // Background colors
  static const Color backgroundLight = Color(0xFFF0F5F9);
  static const Color backgroundMedium = Color(0xFFC9D6DF);
  static const Color backgroundDark = Color(0xFF52616B);
  
  // Border colors
  static const Color borderLight = Color(0xFFC9D6DF);
  static const Color borderMedium = Color(0xFF52616B);
  
  // Shadow colors
  static const Color shadowLight = Color(0x1A1E2022);
  static const Color shadowMedium = Color(0x331E2022);
  static const Color shadowDark = Color(0x4D1E2022);
} 