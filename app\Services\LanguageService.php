<?php

namespace App\Services;

use App\Models\Language;
use App\Models\Translation;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\File;

class LanguageService
{


    /**
     * Set default language
     */
    public function setDefaultLanguage(string $locale): bool
    {
        $language = Language::where('locale', $locale)->first();
        
        if (!$language) {
            return false;
        }

        // Remove default from all languages
        Language::query()->update(['is_default' => false]);
        
        // Set new default
        $language->update([
            'is_default' => true,
            'is_active' => true
        ]);

        // Clear cache
        $this->clearLanguageCache();

        return true;
    }

    /**
     * Toggle language active status
     */
    public function toggleLanguageStatus(string $locale): bool
    {
        $language = Language::where('locale', $locale)->first();
        
        if (!$language) {
            return false;
        }

        // Don't deactivate default language
        if ($language->is_default && $language->is_active) {
            return false;
        }

        $language->update(['is_active' => !$language->is_active]);
        $this->clearLanguageCache();

        return true;
    }

    /**
     * Create new language
     */
    public function createLanguage(array $data): Language
    {
        $language = Language::create($data);
        $this->clearLanguageCache();
        
        return $language;
    }

    /**
     * Update language
     */
    public function updateLanguage(string $locale, array $data): bool
    {
        $language = Language::where('locale', $locale)->first();
        
        if (!$language) {
            return false;
        }

        $language->update($data);
        $this->clearLanguageCache();

        return true;
    }

    /**
     * Delete language
     */
    public function deleteLanguage(string $locale): bool
    {
        $language = Language::where('locale', $locale)->first();
        
        if (!$language || $language->is_default) {
            return false;
        }

        // Delete all translations for this language
        Translation::where('locale', $locale)->delete();
        
        $language->delete();
        $this->clearLanguageCache();

        return true;
    }



    /**
     * Sync translations with lang files
     */
    public function syncWithLangFiles(string $direction = 'both', string $locale = null, bool $force = false): array
    {
        $result = ['success' => true, 'message' => '', 'details' => []];
        
        try {
            $locales = $locale ? [$locale] : Language::getAvailableLocales();
            
            foreach ($locales as $currentLocale) {
                switch ($direction) {
                    case 'files-to-db':
                        $details = $this->syncFilesToDb($currentLocale, $force);
                        break;
                    case 'db-to-files':
                        $details = $this->syncDbToFiles($currentLocale, $force);
                        break;
                    case 'both':
                        $details1 = $this->syncFilesToDb($currentLocale, $force);
                        $details2 = $this->syncDbToFiles($currentLocale, $force);
                        $details = array_merge($details1, $details2);
                        break;
                    default:
                        throw new \InvalidArgumentException('Invalid sync direction');
                }
                
                $result['details'][$currentLocale] = $details;
            }
            
            $result['message'] = 'Sync completed successfully';
            
        } catch (\Exception $e) {
            $result['success'] = false;
            $result['message'] = $e->getMessage();
        }
        
        return $result;
    }

    /**
     * Sync from files to database
     */
    protected function syncFilesToDb(string $locale, bool $force): array
    {
        $langPath = resource_path("lang/{$locale}.json");
        
        if (!File::exists($langPath)) {
            return ['error' => "Lang file not found: {$langPath}"];
        }

        $jsonContent = File::get($langPath);
        $translations = json_decode($jsonContent, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            return ['error' => "Invalid JSON in file: {$langPath}"];
        }

        $imported = 0;
        $updated = 0;
        $skipped = 0;

        foreach ($translations as $key => $value) {
            if (!is_string($value)) {
                continue;
            }

            $existing = Translation::where('key', $key)->where('locale', $locale)->first();

            if ($existing) {
                if ($force || empty($existing->value)) {
                    $existing->update(['value' => $value]);
                    $updated++;
                } else {
                    $skipped++;
                }
            } else {
                Translation::create(['key' => $key, 'locale' => $locale, 'value' => $value]);
                $imported++;
            }
        }

        return compact('imported', 'updated', 'skipped');
    }

    /**
     * Sync from database to files
     */
    protected function syncDbToFiles(string $locale, bool $force): array
    {
        $translations = Translation::where('locale', $locale)->pluck('value', 'key')->toArray();
        
        if (empty($translations)) {
            return ['error' => "No translations found in database for locale: {$locale}"];
        }

        $langPath = resource_path("lang/{$locale}.json");
        $existingTranslations = [];

        if (File::exists($langPath)) {
            $existingContent = File::get($langPath);
            $existingTranslations = json_decode($existingContent, true) ?? [];
        }

        $mergedTranslations = $force ? $translations : array_merge($existingTranslations, $translations);
        ksort($mergedTranslations);

        $jsonContent = json_encode($mergedTranslations, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);

        $langDir = dirname($langPath);
        if (!File::exists($langDir)) {
            File::makeDirectory($langDir, 0755, true);
        }

        File::put($langPath, $jsonContent);

        return ['updated_file' => $langPath, 'translations_count' => count($mergedTranslations)];
    }

    /**
     * Clear language-related cache
     */
    public function clearLanguageCache(): void
    {
        Cache::forget('default_locale');
        Cache::forget('available_locales');
        Cache::forget('default_language');
        Cache::forget('active_languages');
        
        // Clear translation cache
        Cache::flush();
    }

    /**
     * Get language statistics (optimized with single query)
     */
    public function getLanguageStats(): array
    {
        return Cache::remember('language_stats', 1800, function () {
            // Single query with join to get all data at once
            $stats = Language::where('is_active', true)
                ->leftJoin('translations', 'languages.locale', '=', 'translations.locale')
                ->selectRaw('languages.locale, languages.name, languages.is_default, COUNT(translations.id) as translation_count')
                ->groupBy('languages.locale', 'languages.name', 'languages.is_default')
                ->get()
                ->keyBy('locale')
                ->map(function ($item) {
                    return [
                        'name' => $item->name,
                        'is_default' => (bool) $item->is_default,
                        'translation_count' => (int) $item->translation_count,
                    ];
                })
                ->toArray();

            return $stats;
        });
    }
}
