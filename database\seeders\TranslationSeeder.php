<?php

namespace Database\Seeders;

use App\Models\Language;
use App\Models\Translation;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\File;

class TranslationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all active languages
        $languages = Language::where('is_active', true)->get();

        foreach ($languages as $language) {
            $this->seedTranslationsForLanguage($language->locale);
        }

        // Add some additional common translations
        $this->seedCommonTranslations();
    }

    /**
     * Seed translations from lang files for a specific language
     */
    protected function seedTranslationsForLanguage(string $locale): void
    {
        $langFile = resource_path("lang/{$locale}.json");

        if (File::exists($langFile)) {
            $translations = json_decode(File::get($langFile), true);

            if (is_array($translations)) {
                foreach ($translations as $key => $value) {
                    Translation::updateOrCreate(
                        ['key' => $key, 'locale' => $locale],
                        ['value' => $value]
                    );
                }

                $this->command->info("Seeded " . count($translations) . " translations for {$locale}");
            }
        } else {
            $this->command->warn("Lang file not found: {$langFile}");
        }
    }

    /**
     * Seed common translations that might be missing
     */
    protected function seedCommonTranslations(): void
    {
        $commonTranslations = [
            // Navigation
            'nav.home' => [
                'en' => 'Home',
                'vi' => 'Trang chủ',
            ],
            'nav.about' => [
                'en' => 'About',
                'vi' => 'Giới thiệu',
            ],
            'nav.contact' => [
                'en' => 'Contact',
                'vi' => 'Liên hệ',
            ],
            'nav.blog' => [
                'en' => 'Blog',
                'vi' => 'Blog',
            ],
            'nav.events' => [
                'en' => 'Events',
                'vi' => 'Sự kiện',
            ],
            'nav.how_it_works' => [
                'en' => 'How it works',
                'vi' => 'Cách hoạt động',
            ],
            'nav.communities' => [
                'en' => 'Communities',
                'vi' => 'Cộng đồng',
            ],
            'nav.experts' => [
                'en' => 'Experts',
                'vi' => 'Chuyên gia',
            ],
            'nav.login' => [
                'en' => 'Login',
                'vi' => 'Đăng nhập',
            ],
            'nav.register' => [
                'en' => 'Register',
                'vi' => 'Đăng ký',
            ],
            'nav.profile' => [
                'en' => 'Profile',
                'vi' => 'Trang cá nhân',
            ],
            'nav.logout' => [
                'en' => 'Logout',
                'vi' => 'Đăng xuất',
            ],

            // Common actions
            'action.save' => [
                'en' => 'Save',
                'vi' => 'Lưu',
            ],
            'action.cancel' => [
                'en' => 'Cancel',
                'vi' => 'Hủy',
            ],
            'action.delete' => [
                'en' => 'Delete',
                'vi' => 'Xóa',
            ],
            'action.edit' => [
                'en' => 'Edit',
                'vi' => 'Sửa',
            ],
            'action.create' => [
                'en' => 'Create',
                'vi' => 'Tạo',
            ],
            'action.update' => [
                'en' => 'Update',
                'vi' => 'Cập nhật',
            ],
            'action.submit' => [
                'en' => 'Submit',
                'vi' => 'Gửi',
            ],
            'action.search' => [
                'en' => 'Search',
                'vi' => 'Tìm kiếm',
            ],

            // Status messages
            'status.success' => [
                'en' => 'Success',
                'vi' => 'Thành công',
            ],
            'status.error' => [
                'en' => 'Error',
                'vi' => 'Lỗi',
            ],
            'status.warning' => [
                'en' => 'Warning',
                'vi' => 'Cảnh báo',
            ],
            'status.info' => [
                'en' => 'Information',
                'vi' => 'Thông tin',
            ],

            // Form labels
            'form.name' => [
                'en' => 'Name',
                'vi' => 'Tên',
            ],
            'form.email' => [
                'en' => 'Email',
                'vi' => 'Email',
            ],
            'form.password' => [
                'en' => 'Password',
                'vi' => 'Mật khẩu',
            ],
            'form.confirm_password' => [
                'en' => 'Confirm Password',
                'vi' => 'Xác nhận mật khẩu',
            ],
            'form.phone' => [
                'en' => 'Phone',
                'vi' => 'Số điện thoại',
            ],
            'form.address' => [
                'en' => 'Address',
                'vi' => 'Địa chỉ',
            ],
            'form.message' => [
                'en' => 'Message',
                'vi' => 'Tin nhắn',
            ],
            'form.description' => [
                'en' => 'Description',
                'vi' => 'Mô tả',
            ],
            'form.title' => [
                'en' => 'Title',
                'vi' => 'Tiêu đề',
            ],
            'form.content' => [
                'en' => 'Content',
                'vi' => 'Nội dung',
            ],

            // Time and date
            'time.today' => [
                'en' => 'Today',
                'vi' => 'Hôm nay',
            ],
            'time.yesterday' => [
                'en' => 'Yesterday',
                'vi' => 'Hôm qua',
            ],
            'time.tomorrow' => [
                'en' => 'Tomorrow',
                'vi' => 'Ngày mai',
            ],
            'time.now' => [
                'en' => 'Now',
                'vi' => 'Bây giờ',
            ],

            // Pagination
            'pagination.previous' => [
                'en' => 'Previous',
                'vi' => 'Trước',
            ],
            'pagination.next' => [
                'en' => 'Next',
                'vi' => 'Tiếp',
            ],
            'pagination.first' => [
                'en' => 'First',
                'vi' => 'Đầu',
            ],
            'pagination.last' => [
                'en' => 'Last',
                'vi' => 'Cuối',
            ],

            // Common messages
            'message.no_data' => [
                'en' => 'No data available',
                'vi' => 'Không có dữ liệu',
            ],
            'message.loading' => [
                'en' => 'Loading...',
                'vi' => 'Đang tải...',
            ],
            'message.confirm_delete' => [
                'en' => 'Are you sure you want to delete this item?',
                'vi' => 'Bạn có chắc chắn muốn xóa mục này?',
            ],
            'message.operation_successful' => [
                'en' => 'Operation completed successfully',
                'vi' => 'Thao tác hoàn thành thành công',
            ],
            'message.operation_failed' => [
                'en' => 'Operation failed',
                'vi' => 'Thao tác thất bại',
            ],

            // Language switcher
            'lang.switch_language' => [
                'en' => 'Switch Language',
                'vi' => 'Chuyển ngôn ngữ',
            ],
            'lang.current_language' => [
                'en' => 'Current Language',
                'vi' => 'Ngôn ngữ hiện tại',
            ],

            // Test page translations
            'test.title' => [
                'en' => 'Language Switching Test',
                'vi' => 'Test Chuyển Đổi Ngôn Ngữ',
            ],
            'test.current_info' => [
                'en' => 'Current Language Info',
                'vi' => 'Thông Tin Ngôn Ngữ Hiện Tại',
            ],
            'test.current_locale' => [
                'en' => 'Current Locale',
                'vi' => 'Locale Hiện Tại',
            ],
            'test.current_language' => [
                'en' => 'Current Language',
                'vi' => 'Ngôn Ngữ Hiện Tại',
            ],
            'test.flag' => [
                'en' => 'Flag',
                'vi' => 'Cờ',
            ],
            'test.current_url' => [
                'en' => 'Current URL',
                'vi' => 'URL Hiện Tại',
            ],
            'test.language_switchers' => [
                'en' => 'Language Switchers',
                'vi' => 'Bộ Chuyển Đổi Ngôn Ngữ',
            ],
            'test.translation_examples' => [
                'en' => 'Translation Examples',
                'vi' => 'Ví Dụ Bản Dịch',
            ],
            'test.available_languages' => [
                'en' => 'Available Languages',
                'vi' => 'Ngôn Ngữ Có Sẵn',
            ],
            'test.switch_to' => [
                'en' => 'Switch to',
                'vi' => 'Chuyển sang',
            ],
            'test.test_links' => [
                'en' => 'Test Links',
                'vi' => 'Liên Kết Test',
            ],
        ];

        $languages = Language::where('is_active', true)->pluck('locale')->toArray();

        foreach ($commonTranslations as $key => $translations) {
            foreach ($languages as $locale) {
                if (isset($translations[$locale])) {
                    Translation::updateOrCreate(
                        ['key' => $key, 'locale' => $locale],
                        ['value' => $translations[$locale]]
                    );
                }
            }
        }

        $this->command->info("Seeded common translations for " . count($languages) . " languages");
    }
}
