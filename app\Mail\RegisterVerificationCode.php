<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class RegisterVerificationCode extends Mailable
{
    use Queueable, SerializesModels;

    public $code;
    public $email;

    public function __construct($code, $email)
    {
        $this->code = $code;
        $this->email = $email;
    }

    public function build()
    {
        return $this->subject('Mã xác thực đăng ký tài khoản')
            ->view('emails.register-verification-code')
            ->with([
                'code' => $this->code,
                'email' => $this->email,
            ]);
    }
} 