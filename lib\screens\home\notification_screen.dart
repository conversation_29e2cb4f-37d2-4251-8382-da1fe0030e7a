import 'package:flutter/material.dart';

class NotificationScreen extends StatelessWidget {
  const NotificationScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final List<Map<String, dynamic>> notifications = [
      {
        'title': 'Bạn đã tham gia dự án "Làm sạch bãi biển"',
        'time': '2 phút trước',
        'icon': Icons.beach_access,
        'color': const Color(0xFF10B981),
      },
      {
        'title': 'Báo cáo của bạn đã được duyệt',
        'time': '1 giờ trước',
        'icon': Icons.check_circle_outline,
        'color': const Color(0xFF0EA5E9),
      },
      {
        'title': 'Có 3 bình luận mới trong dự án bạn theo dõi',
        'time': '3 giờ trước',
        'icon': Icons.forum_outlined,
        'color': const Color(0xFF8B5CF6),
      },
      {
        'title': '<PERSON><PERSON> kiệ<PERSON> "Trồng c<PERSON>y xanh" sắp diễn ra',
        'time': 'Hôm qua',
        'icon': Icons.event,
        'color': const Color(0xFFEC4899),
      },
    ];

    return Scaffold(
      appBar: AppBar(
        title: const Text('Thông báo', style: TextStyle(fontWeight: FontWeight.bold)),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 1,
        centerTitle: true,
      ),
      body: ListView.separated(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        itemCount: notifications.length,
        separatorBuilder: (context, i) => const Divider(height: 1),
        itemBuilder: (context, i) {
          final n = notifications[i];
          return ListTile(
            leading: CircleAvatar(
              backgroundColor: n['color'].withOpacity(0.15),
              child: Icon(n['icon'], color: n['color']),
            ),
            title: Text(n['title'], style: const TextStyle(fontWeight: FontWeight.w500)),
            subtitle: Text(n['time'], style: const TextStyle(color: Colors.grey, fontSize: 13)),
            onTap: () {},
          );
        },
      ),
    );
  }
} 