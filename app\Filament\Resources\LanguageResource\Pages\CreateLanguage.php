<?php

namespace App\Filament\Resources\LanguageResource\Pages;

use App\Filament\Resources\LanguageResource;
use App\Models\Language;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Facades\Cache;

class CreateLanguage extends CreateRecord
{
    protected static string $resource = LanguageResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // If this is set as default, make sure no other language is default
        if ($data['is_default'] ?? false) {
            Language::query()->update(['is_default' => false]);
            $data['is_active'] = true; // Default language must be active
        }

        // Set default values
        $data['is_active'] = $data['is_active'] ?? true;

        return $data;
    }

    protected function afterCreate(): void
    {
        // Clear cache after creating new language
        Cache::forget('default_locale');
        Cache::forget('available_locales');
    }
}
