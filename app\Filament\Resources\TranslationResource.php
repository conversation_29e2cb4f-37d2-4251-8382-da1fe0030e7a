<?php

namespace App\Filament\Resources;

use App\Filament\Resources\TranslationResource\Pages;
use App\Models\Language;
use App\Models\Translation;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Cache;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Artisan;
use Filament\Tables\Actions\Action;

class TranslationResource extends Resource
{
    protected static ?string $model = Translation::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static ?string $navigationGroup = 'Localization';

    protected static ?string $navigationLabel = 'Translations';

    protected static ?int $navigationSort = 2;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('key')
                    ->label('Translation Key')
                    ->required()
                    ->maxLength(255)
                    ->placeholder('welcome_message, login_button, etc.')
                    ->helperText('Use dot notation for nested keys: auth.login.title')
                    ->disabled(fn ($livewire) => $livewire instanceof \Filament\Resources\Pages\EditRecord)
                    ->rules([
                        function () {
                            return function (string $attribute, $value, \Closure $fail) {
                                $locale = request()->input('locale');
                                $recordId = request()->route('record'); // For edit mode

                                $query = \App\Models\Translation::where('key', $value)
                                    ->where('locale', $locale);

                                // Exclude current record when editing
                                if ($recordId) {
                                    $query->where('id', '!=', $recordId);
                                }

                                if ($query->exists()) {
                                    $fail("Translation key '{$value}' already exists for this language.");
                                }
                            };
                        },
                    ]),

                Forms\Components\Select::make('locale')
                    ->label('Language')
                    ->required()
                    ->options(function () {
                        return Language::where('is_active', true)
                            ->pluck('name', 'locale')
                            ->toArray();
                    })
                    ->searchable()
                    ->preload(),

                Forms\Components\Textarea::make('value')
                    ->label('Translation Value')
                    ->required()
                    ->rows(3)
                    ->placeholder('Enter the translated text here...')
                    ->helperText('The actual translated text for this key'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('key')
                    ->label('Key')
                    ->searchable()
                    ->sortable()
                    ->copyable()
                    ->limit(50),

                Tables\Columns\TextColumn::make('locale')
                    ->label('Language')
                    ->searchable()
                    ->sortable()
                    ->badge()
                    ->color('primary'),

                Tables\Columns\TextColumn::make('language.name')
                    ->label('Language Name')
                    ->searchable()
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('value')
                    ->label('Translation')
                    ->searchable()
                    ->limit(100)
                    ->tooltip(function (Translation $record): string {
                        return $record->value;
                    }),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Updated')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('locale')
                    ->label('Language')
                    ->options(function () {
                        return Language::where('is_active', true)
                            ->pluck('name', 'locale')
                            ->toArray();
                    })
                    ->searchable()
                    ->preload(),

                Tables\Filters\Filter::make('missing_translations')
                    ->label('Missing Translations')
                    ->query(function (Builder $query) {
                        $allKeys = Translation::distinct()->pluck('key');
                        $allLocales = Language::where('is_active', true)->pluck('locale');
                        
                        $missingCombinations = [];
                        foreach ($allKeys as $key) {
                            foreach ($allLocales as $locale) {
                                if (!Translation::where('key', $key)->where('locale', $locale)->exists()) {
                                    $missingCombinations[] = ['key' => $key, 'locale' => $locale];
                                }
                            }
                        }
                        
                        if (empty($missingCombinations)) {
                            return $query->whereRaw('1 = 0'); // No results
                        }
                        
                        return $query->where(function ($q) use ($missingCombinations) {
                            foreach ($missingCombinations as $combination) {
                                $q->orWhere(function ($subQ) use ($combination) {
                                    $subQ->where('key', $combination['key'])
                                         ->where('locale', $combination['locale']);
                                });
                            }
                        });
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->after(function () {
                        // Only clear cache, don't regenerate files
                        Cache::flush();
                    }),
                Tables\Actions\ReplicateAction::make()
                    ->form([
                        Forms\Components\Select::make('locale')
                            ->label('Target Language')
                            ->required()
                            ->options(function () {
                                return Language::where('is_active', true)
                                    ->pluck('name', 'locale')
                                    ->toArray();
                            })
                            ->searchable()
                            ->preload(),
                    ])
                    ->beforeReplicaSaved(function (array $data, Translation $replica): void {
                        $replica->locale = $data['locale'];
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->requiresConfirmation()
                        ->modalHeading('Delete Selected Translations')
                        ->modalDescription('Are you sure you want to delete the selected translations from the database? This will NOT affect JSON files. This action cannot be undone.')
                        ->modalSubmitActionLabel('Yes, Delete')
                        ->action(function (\Illuminate\Database\Eloquent\Collection $records) {
                            // Increase time and memory limits for large deletions
                            set_time_limit(300); // 5 minutes
                            ini_set('memory_limit', '512M');

                            $totalRecords = $records->count();
                            $totalDeleted = 0;
                            $recordIds = $records->pluck('id')->toArray();

                            // Show initial notification for large operations
                            if ($totalRecords > 100) {
                                \Filament\Notifications\Notification::make()
                                    ->title('Processing Bulk Delete')
                                    ->body("Deleting {$totalRecords} translations... This may take a moment.")
                                    ->info()
                                    ->send();
                            }

                            // Process deletions in chunks to avoid timeout
                            $chunks = array_chunk($recordIds, 500);

                            foreach ($chunks as $chunkIndex => $chunk) {
                                $deleted = Translation::whereIn('id', $chunk)->delete();
                                $totalDeleted += $deleted;

                                // Show progress for very large operations
                                if ($totalRecords > 1000 && ($chunkIndex + 1) % 5 === 0) {
                                    $progress = round(($chunkIndex + 1) / count($chunks) * 100);
                                    \Filament\Notifications\Notification::make()
                                        ->title("Delete Progress: {$progress}%")
                                        ->body("Processed " . ($chunkIndex + 1) . " of " . count($chunks) . " chunks")
                                        ->info()
                                        ->send();
                                }
                            }

                            // Clear cache only once after all deletions
                            Cache::flush();

                            // Show success notification
                            \Filament\Notifications\Notification::make()
                                ->title('Bulk Delete Completed')
                                ->body("Successfully deleted {$totalDeleted} translations from database")
                                ->success()
                                ->send();
                        }),

                    Tables\Actions\BulkAction::make('delete_by_locale')
                        ->label('Delete All by Locale')
                        ->icon('heroicon-o-trash')
                        ->color('danger')
                        ->requiresConfirmation()
                        ->modalHeading('Delete All Translations by Locale')
                        ->modalDescription('This will delete ALL translations for the selected locales from the database only. JSON files will NOT be affected. This action cannot be undone.')
                        ->form([
                            Forms\Components\Select::make('locales')
                                ->label('Select Locales to Delete')
                                ->multiple()
                                ->options(function () {
                                    return \App\Models\Language::where('is_active', true)
                                        ->pluck('name', 'locale')
                                        ->toArray();
                                })
                                ->required()
                                ->helperText('All translations for selected locales will be permanently deleted'),
                        ])
                        ->action(function (array $data) {
                            set_time_limit(300);
                            ini_set('memory_limit', '512M');

                            $locales = $data['locales'];
                            $totalDeleted = 0;

                            foreach ($locales as $locale) {
                                $count = Translation::where('locale', $locale)->count();

                                if ($count > 0) {
                                    \Filament\Notifications\Notification::make()
                                        ->title("Deleting {$locale} translations")
                                        ->body("Processing {$count} translations...")
                                        ->info()
                                        ->send();

                                    // Delete in chunks
                                    $deleted = 0;
                                    do {
                                        $chunkDeleted = Translation::where('locale', $locale)
                                            ->limit(500)
                                            ->delete();
                                        $deleted += $chunkDeleted;
                                    } while ($chunkDeleted > 0);

                                    $totalDeleted += $deleted;
                                }
                            }

                            // Clear cache only
                            Cache::flush();

                            \Filament\Notifications\Notification::make()
                                ->title('Locale Delete Completed')
                                ->body("Successfully deleted {$totalDeleted} translations from " . count($locales) . " locale(s) in database")
                                ->success()
                                ->send();
                        }),
                    Tables\Actions\BulkAction::make('duplicate_to_language')
                        ->label('Duplicate to Language')
                        ->icon('heroicon-o-document-duplicate')
                        ->color('info')
                        ->form([
                            Forms\Components\Select::make('target_locale')
                                ->label('Target Language')
                                ->required()
                                ->options(function () {
                                    return Language::where('is_active', true)
                                        ->pluck('name', 'locale')
                                        ->toArray();
                                })
                                ->searchable()
                                ->preload(),
                        ])
                        ->action(function ($records, array $data) {
                            $targetLocale = $data['target_locale'];
                            $created = 0;
                            $skipped = 0;

                            foreach ($records as $record) {
                                $exists = Translation::where('key', $record->key)
                                    ->where('locale', $targetLocale)
                                    ->exists();

                                if (!$exists) {
                                    Translation::create([
                                        'key' => $record->key,
                                        'locale' => $targetLocale,
                                        'value' => $record->value,
                                    ]);
                                    $created++;
                                } else {
                                    $skipped++;
                                }
                            }

                            Cache::flush(); // Clear all translation cache

                            Notification::make()
                                ->title("Duplicated {$created} translations, skipped {$skipped} existing ones")
                                ->success()
                                ->send();
                        }),
                ]),
            ])
            ->defaultSort('key');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTranslations::route('/'),
            'create' => Pages\CreateTranslation::route('/create'),
            'edit' => Pages\EditTranslation::route('/{record}/edit'),
        ];
    }
}
