import 'package:flutter/material.dart';
import '../splash_screen.dart';
import 'map_screen.dart';
import 'report_screen.dart';
import 'event_screen.dart';
import 'forum_screen.dart';
import 'notification_screen.dart';
import '../auth/login_screen.dart';
import '../account/profile_screen.dart';
import '../account/statistic_screen.dart';
import '../account/event_menu_screen.dart';
import '../account/gift_screen.dart';
import '../../constants/colors.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _selectedIndex = 0;
  late BuildContext _scaffoldContext;

  final List<String> _titles = [
    'Trang chủ',
    'Bản đồ',
    'Báo cáo',
    'Sự kiện',
    '<PERSON><PERSON><PERSON> đ<PERSON>n',
  ];

  Widget _getBody() {
    switch (_selectedIndex) {
      case 0:
        return const HomeTabSample();
      case 1:
        return const MapScreen();
      case 2:
        return const ReportScreen();
      case 3:
        return const EventScreen();
      case 4:
        return const ForumScreen();
      default:
        return const Center(child: Text('Trang chủ'));
    }
  }

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  void _onReportPressed() {
    setState(() {
      _selectedIndex = 2;
    });
    // TODO: Xử lý logic báo cáo
  }

  void _openScreen(Widget screen) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => screen),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Builder(
      builder: (context) {
        _scaffoldContext = context;
        return Scaffold(
          backgroundColor: AppColors.background,
          appBar: AppBar(
            backgroundColor: AppColors.background,
            elevation: 0,
            titleSpacing: 0,
            leadingWidth: 0,
            title: Row(
              children: [
                const SizedBox(width: 16),
                Text(
                  'EcoSolves',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.w700,
                    letterSpacing: 1.2,
                    color: AppColors.textDark,
                    fontFamily: 'Roboto',
                  ),
                ),
                const Spacer(),
                IconButton(
                  icon: Icon(Icons.notifications_none, color: AppColors.primary, size: 28),
                  onPressed: () {
                    Navigator.of(_scaffoldContext).push(
                      MaterialPageRoute(builder: (context) => const NotificationScreen()),
                    );
                  },
                ),
                IconButton(
                  icon: Icon(Icons.account_circle, color: AppColors.primary, size: 28),
                  onPressed: () {
                    Navigator.of(_scaffoldContext).push(
                      MaterialPageRoute(builder: (context) => const ProfileScreen()),
                    );
                  },
                ),
                const SizedBox(width: 8),
              ],
            ),
          ),
          body: _getBody(),
          floatingActionButton: SizedBox(
            height: 72,
            width: 72,
            child: FloatingActionButton(
              onPressed: _onReportPressed,
              backgroundColor: AppColors.primary,
              elevation: 4,
              shape: const CircleBorder(),
              child: const Icon(Icons.add, size: 36, color: AppColors.white),
            ),
          ),
          floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
          bottomNavigationBar: BottomAppBar(
            shape: const CircularNotchedRectangle(),
            notchMargin: 8,
            elevation: 0,
            color: AppColors.white,
            child: Container(
              decoration: BoxDecoration(
                border: Border(
                  top: BorderSide(color: AppColors.borderLight, width: 1),
                ),
              ),
              child: SizedBox(
                height: 70,
                child: Row(
                  children: [
                    Expanded(
                      child: _buildNavItem(
                        icon: Icons.home_outlined,
                        label: 'Home',
                        index: 0,
                      ),
                    ),
                    Expanded(
                      child: _buildNavItem(
                        icon: Icons.map_outlined,
                        label: 'Bản đồ',
                        index: 1,
                      ),
                    ),
                    const Expanded(child: SizedBox()), // Chỗ trống cho FAB
                    Expanded(
                      child: _buildNavItem(
                        icon: Icons.event_outlined,
                        label: 'Sự kiện',
                        index: 3,
                      ),
                    ),
                    Expanded(
                      child: _buildNavItem(
                        icon: Icons.forum_outlined,
                        label: 'Diễn đàn',
                        index: 4,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildNavItem({required IconData icon, required String label, required int index}) {
    final bool isSelected = _selectedIndex == index;
    final Color activeColor = AppColors.primary;
    final Color inactiveColor = AppColors.textSecondary;
    return MaterialButton(
      minWidth: 40,
      onPressed: () => _onItemTapped(index),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: isSelected ? activeColor : inactiveColor, size: 28),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              color: isSelected ? activeColor : inactiveColor,
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
              fontSize: 13,
              fontFamily: 'Roboto',
            ),
          ),
        ],
      ),
    );
  }
}

class HomeTabSample extends StatefulWidget {
  const HomeTabSample({super.key});

  @override
  State<HomeTabSample> createState() => _HomeTabSampleState();
}

class _HomeTabSampleState extends State<HomeTabSample> {
  final List<String> bannerImages = [
    'https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=800&q=80',
    'https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=crop&w=800&q=80',
    'https://images.unsplash.com/photo-1465101178521-c1a9136a3b99?auto=format&fit=crop&w=800&q=80',
  ];
  final List<Map<String, dynamic>> stats = [
    {'label': 'Số báo cáo', 'value': 123, 'icon': Icons.report, 'color': AppColors.primary},
    {'label': 'Số dự án', 'value': 8, 'icon': Icons.work_outline, 'color': AppColors.textSecondary},
    {'label': 'Tham gia dự án', 'value': 21, 'icon': Icons.group, 'color': AppColors.primary},
    {'label': 'Bình luận', 'value': 56, 'icon': Icons.comment, 'color': AppColors.textSecondary},
  ];
  final List<Map<String, dynamic>> topMembers = List.generate(10, (i) => {
    'name': 'Thành viên ${i+1}',
    'score': 1000 - i * 73,
    'avatar': 'https://i.pravatar.cc/150?img=${i+1}',
  });
  final List<Map<String, dynamic>> posts = [
    {
      'title': 'Bài viết mới về bảo vệ môi trường',
      'image': 'https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=crop&w=800&q=80',
      'author': 'Nguyễn Văn A',
    },
    {
      'title': 'Cách phân loại rác hiệu quả',
      'image': 'https://images.unsplash.com/photo-1465101178521-c1a9136a3b99?auto=format&fit=crop&w=800&q=80',
      'author': 'Trần Thị B',
    },
    {
      'title': 'Tham gia dự án xanh cùng EcoSolves',
      'image': 'https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=800&q=80',
      'author': 'Lê Văn C',
    },
  ];

  late final PageController _bannerController;
  late final PageController _postController;
  int _bannerIndex = 0;
  int _postIndex = 0;

  @override
  void initState() {
    super.initState();
    _bannerController = PageController(viewportFraction: 0.9);
    _postController = PageController(viewportFraction: 0.85);
  }

  @override
  void dispose() {
    _bannerController.dispose();
    _postController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Banner slide
          SizedBox(
            height: 170,
            child: Column(
              children: [
                Expanded(
                  child: PageView.builder(
                    controller: _bannerController,
                    itemCount: bannerImages.length,
                    onPageChanged: (i) => setState(() => _bannerIndex = i),
                    itemBuilder: (context, index) {
                      return Container(
                        margin: const EdgeInsets.symmetric(horizontal: 4),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(16),
                          image: DecorationImage(
                            image: NetworkImage(bannerImages[index]),
                            fit: BoxFit.cover,
                          ),
                        ),
                      );
                    },
                  ),
                ),
                const SizedBox(height: 6),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: List.generate(
                    bannerImages.length,
                    (i) => AnimatedContainer(
                      duration: const Duration(milliseconds: 300),
                      margin: const EdgeInsets.symmetric(horizontal: 3),
                      width: _bannerIndex == i ? 18 : 7,
                      height: 7,
                      decoration: BoxDecoration(
                        color: _bannerIndex == i ? AppColors.primary : AppColors.surface,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 18),
          // 4 ô thống kê
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: stats.length,
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              mainAxisSpacing: 12,
              crossAxisSpacing: 12,
              childAspectRatio: 2.2,
            ),
            itemBuilder: (context, i) {
              final stat = stats[i];
              return Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(14),
                  color: AppColors.white,
                  border: Border.all(color: AppColors.borderLight, width: 1),
                ),
                padding: const EdgeInsets.all(14),
                child: Row(
                  children: [
                    CircleAvatar(
                      backgroundColor: stat['color'],
                      child: Icon(stat['icon'], color: AppColors.white),
                    ),
                    const SizedBox(width: 12),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          stat['value'].toString(),
                          style: TextStyle(
                            fontSize: 20, 
                            fontWeight: FontWeight.w700,
                            color: AppColors.textDark,
                            fontFamily: 'Roboto',
                          ),
                        ),
                        Text(
                          stat['label'],
                          style: TextStyle(
                            fontSize: 13, 
                            color: AppColors.textMedium,
                            fontFamily: 'Roboto',
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              );
            },
          ),
          const SizedBox(height: 22),
          // Top thành viên
          Text(
            'Top thành viên', 
            style: TextStyle(
              fontSize: 18, 
              fontWeight: FontWeight.w600,
              color: AppColors.textDark,
              fontFamily: 'Roboto',
            )
          ),
          const SizedBox(height: 8),
          Card(
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
              side: BorderSide(color: AppColors.borderLight, width: 1),
            ),
            child: ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: topMembers.length,
              separatorBuilder: (context, i) => Divider(height: 1, color: AppColors.borderLight),
              itemBuilder: (context, i) {
                final m = topMembers[i];
                Color borderColor;
                IconData? icon;
                Color? iconColor;
                switch (i) {
                  case 0:
                    borderColor = const Color(0xFFFFD700); // Vàng
                    icon = Icons.emoji_events;
                    iconColor = const Color(0xFFFFD700);
                    break;
                  case 1:
                    borderColor = const Color(0xFFC0C0C0); // Bạc
                    icon = Icons.military_tech;
                    iconColor = const Color(0xFFC0C0C0);
                    break;
                  case 2:
                    borderColor = const Color(0xFFCD7F32); // Đồng
                    icon = Icons.military_tech;
                    iconColor = const Color(0xFFCD7F32);
                    break;
                  default:
                    borderColor = Colors.transparent;
                    icon = null;
                }
                return ListTile(
                  leading: Stack(
                    alignment: Alignment.topRight,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(2.5),
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(color: borderColor, width: i < 3 ? 3 : 0),
                        ),
                        child: CircleAvatar(
                          backgroundImage: NetworkImage(m['avatar']),
                          radius: 22,
                        ),
                      ),
                      if (icon != null)
                        Positioned(
                          right: -2,
                          top: -2,
                          child: Icon(icon, color: iconColor, size: 18),
                        ),
                    ],
                  ),
                  title: Text(
                    m['name'],
                    style: TextStyle(
                      fontWeight: i < 3 ? FontWeight.w600 : FontWeight.w500,
                      color: AppColors.textDark,
                      fontFamily: 'Roboto',
                    ),
                  ),
                  trailing: Text(
                    '${m['score']} điểm',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      color: AppColors.primary,
                      fontFamily: 'Roboto',
                    ),
                  ),
                );
              },
            ),
          ),
          const SizedBox(height: 22),
          // Slide bài viết mới nhất
          Text(
            'Bài viết mới nhất', 
            style: TextStyle(
              fontSize: 18, 
              fontWeight: FontWeight.w600,
              color: AppColors.textDark,
              fontFamily: 'Roboto',
            )
          ),
          const SizedBox(height: 8),
          SizedBox(
            height: 170,
            child: Column(
              children: [
                Expanded(
                  child: PageView.builder(
                    controller: _postController,
                    itemCount: posts.length,
                    onPageChanged: (i) => setState(() => _postIndex = i),
                    itemBuilder: (context, index) {
                      final post = posts[index];
                      return Container(
                        margin: const EdgeInsets.symmetric(horizontal: 6),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(16),
                          image: DecorationImage(
                            image: NetworkImage(post['image']),
                            fit: BoxFit.cover,
                            colorFilter: ColorFilter.mode(AppColors.shadowMedium, BlendMode.darken),
                          ),
                        ),
                        child: Container(
                          padding: const EdgeInsets.all(16),
                          alignment: Alignment.bottomLeft,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(16),
                            gradient: LinearGradient(
                              colors: [Colors.transparent, AppColors.shadowDark],
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                            ),
                          ),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.end,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                post['title'],
                                style: const TextStyle(
                                  fontSize: 16, 
                                  fontWeight: FontWeight.w600, 
                                  color: AppColors.white,
                                  fontFamily: 'Roboto',
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(height: 4),
                              Text(
                                'Tác giả: ${post['author']}',
                                style: const TextStyle(
                                  fontSize: 13, 
                                  color: AppColors.textLight,
                                  fontFamily: 'Roboto',
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ),
                const SizedBox(height: 6),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: List.generate(
                    posts.length,
                    (i) => AnimatedContainer(
                      duration: const Duration(milliseconds: 300),
                      margin: const EdgeInsets.symmetric(horizontal: 3),
                      width: _postIndex == i ? 18 : 7,
                      height: 7,
                      decoration: BoxDecoration(
                        color: _postIndex == i ? AppColors.primary : AppColors.surface,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class GradientText extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final Gradient gradient;

  const GradientText(this.text, {super.key, this.style, required this.gradient});

  @override
  Widget build(BuildContext context) {
    return ShaderMask(
      shaderCallback: (bounds) => gradient.createShader(
        Rect.fromLTWH(0, 0, bounds.width, bounds.height),
      ),
      child: Text(
        text,
        style: (style ?? const TextStyle()).copyWith(color: Colors.white),
      ),
    );
  }
} 