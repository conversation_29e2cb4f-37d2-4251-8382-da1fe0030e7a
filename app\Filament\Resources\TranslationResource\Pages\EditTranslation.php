<?php

namespace App\Filament\Resources\TranslationResource\Pages;

use App\Filament\Resources\TranslationResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Support\Facades\Cache;

class EditTranslation extends EditRecord
{
    protected static string $resource = TranslationResource::class;

    protected $originalKey;
    protected $originalLocale;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make()
                ->after(function () {
                    $this->clearTranslationCache();
                }),
        ];
    }

    protected function beforeSave(): void
    {
        // Store original key and locale before saving
        $this->originalKey = $this->record->getOriginal('key');
        $this->originalLocale = $this->record->getOriginal('locale');
    }

    protected function afterSave(): void
    {
        // Clear specific translation cache
        $this->clearTranslationCache();

        // Regenerate translation files
        $this->regenerateTranslationFiles();

        // Show success notification
        \Filament\Notifications\Notification::make()
            ->title('Translation Updated')
            ->body('Translation has been updated successfully.')
            ->success()
            ->send();
    }

    /**
     * Clear specific translation cache
     */
    private function clearTranslationCache(): void
    {
        $locale = $this->record->locale;
        $key = $this->record->key;

        $cacheKeys = [
            "trans_{$locale}_{$key}",
            "translations_{$locale}",
            "current_language_{$locale}",
            "translation_progress_{$locale}",
            "language_stats",
        ];

        foreach ($cacheKeys as $cacheKey) {
            Cache::forget($cacheKey);
        }
    }

    protected function regenerateTranslationFiles(): void
    {
        try {
            // Call the export command to regenerate translation files
            \Illuminate\Support\Facades\Artisan::call('translations:export-all');

            // Also update the JSON files
            $this->updateJsonTranslations();

        } catch (\Exception $e) {
            // Silently handle errors - files will be regenerated on next request
        }
    }

    protected function updateJsonTranslations(): void
    {
        // Update all_translations.json file
        $translations = \App\Models\Translation::all()->groupBy('locale');
        $allTranslations = [];

        foreach ($translations as $locale => $localeTranslations) {
            $allTranslations[$locale] = [];
            foreach ($localeTranslations as $translation) {
                $allTranslations[$locale][$translation->key] = $translation->value;
            }
        }

        // Save to all_translations.json
        $filePath = storage_path('app/translations/all_translations.json');
        if (!file_exists(dirname($filePath))) {
            mkdir(dirname($filePath), 0755, true);
        }

        file_put_contents($filePath, json_encode($allTranslations, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    }
}
