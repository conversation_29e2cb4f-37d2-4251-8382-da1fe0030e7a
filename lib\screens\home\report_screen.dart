import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';

class ReportScreen extends StatefulWidget {
  const ReportScreen({super.key});

  @override
  State<ReportScreen> createState() => _ReportScreenState();
}

class _ReportScreenState extends State<ReportScreen> {
  final _formKey = GlobalKey<FormState>();
  final _descController = TextEditingController();
  final _latController = TextEditingController();
  final _lngController = TextEditingController();
  final _mapLinkController = TextEditingController();
  File? _imageFile;
  bool _isLoading = false;

  Future<void> _pickImage() async {
    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(source: ImageSource.gallery);
    if (pickedFile != null) {
      setState(() {
        _imageFile = File(pickedFile.path);
      });
    }
  }

  Future<void> _getCurrentLocation() async {
    // TODO: <PERSON><PERSON><PERSON> vị trí GPS thực tế (cần package geolocator)
    // Demo: set vị trí giả lập
    setState(() {
      _latController.text = '10.762622';
      _lngController.text = '106.660172';
    });
  }

  void _submit() {
    if (!_formKey.currentState!.validate()) return;
    setState(() => _isLoading = true);
    // TODO: Gửi dữ liệu lên server
    Future.delayed(const Duration(seconds: 2), () {
      setState(() => _isLoading = false);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Đã gửi báo cáo!'), backgroundColor: Colors.green),
      );
      _formKey.currentState!.reset();
      setState(() {
        _imageFile = null;
        _latController.clear();
        _lngController.clear();
        _mapLinkController.clear();
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Báo cáo điểm rác mới',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold, color: Color(0xFF10B981)),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            const Text(
              'Hãy giúp cộng đồng phát hiện và xử lý các điểm rác chưa được thu gom. Vui lòng điền đầy đủ thông tin bên dưới.',
              style: TextStyle(fontSize: 15),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            // Ảnh điểm rác
            const Text('Ảnh điểm rác *', style: TextStyle(fontWeight: FontWeight.bold, color: Color(0xFF10B981))),
            const SizedBox(height: 8),
            GestureDetector(
              onTap: _pickImage,
              child: Container(
                height: 48,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.teal, width: 1.2),
                  borderRadius: BorderRadius.circular(8),
                  color: Colors.white,
                ),
                child: Row(
                  children: [
                    const SizedBox(width: 12),
                    const Icon(Icons.attach_file, color: Colors.teal),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _imageFile != null ? _imageFile!.path.split('/').last : 'Chọn ảnh... (bắt buộc)',
                        style: TextStyle(color: _imageFile != null ? Colors.black : Colors.grey),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    if (_imageFile != null)
                      IconButton(
                        icon: const Icon(Icons.clear, color: Colors.red),
                        onPressed: () => setState(() => _imageFile = null),
                      ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),
            // Mô tả
            const Text('Mô tả *', style: TextStyle(fontWeight: FontWeight.bold, color: Color(0xFF10B981))),
            const SizedBox(height: 8),
            TextFormField(
              controller: _descController,
              minLines: 3,
              maxLines: 5,
              decoration: const InputDecoration(
                hintText: 'Ví dụ: Đống rác lớn cạnh trường học...',
                border: OutlineInputBorder(),
                filled: true,
                fillColor: Colors.white,
              ),
              validator: (v) => v == null || v.trim().isEmpty ? 'Vui lòng nhập mô tả' : null,
            ),
            const SizedBox(height: 20),
            // Vị trí GPS
            const Text('Vị trí GPS *', style: TextStyle(fontWeight: FontWeight.bold, color: Color(0xFF10B981))),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _latController,
                    keyboardType: TextInputType.numberWithOptions(decimal: true),
                    decoration: const InputDecoration(
                      hintText: 'Vĩ độ (Latitude)',
                      border: OutlineInputBorder(),
                      filled: true,
                      fillColor: Colors.white,
                    ),
                    validator: (v) => (v == null || v.trim().isEmpty) && _mapLinkController.text.trim().isEmpty ? 'Bắt buộc' : null,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: TextFormField(
                    controller: _lngController,
                    keyboardType: TextInputType.numberWithOptions(decimal: true),
                    decoration: const InputDecoration(
                      hintText: 'Kinh độ (Longitude)',
                      border: OutlineInputBorder(),
                      filled: true,
                      fillColor: Colors.white,
                    ),
                    validator: (v) => (v == null || v.trim().isEmpty) && _mapLinkController.text.trim().isEmpty ? 'Bắt buộc' : null,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                ElevatedButton(
                  onPressed: _getCurrentLocation,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF0EA5E9),
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                  ),
                  child: const Text('Thử lại lấy vị trí'),
                ),
                const SizedBox(width: 8),
                const Expanded(
                  child: Text(
                    'Không thể tự động lấy vị trí, vui lòng kiểm tra cài đặt định vị và thử lại.',
                    style: TextStyle(fontSize: 12, color: Colors.grey),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // Link Google Maps
            const Text('Hoặc dán link Google Maps vị trí điểm rác:', style: TextStyle(fontWeight: FontWeight.bold, color: Color(0xFF10B981))),
            const SizedBox(height: 8),
            TextFormField(
              controller: _mapLinkController,
              decoration: const InputDecoration(
                hintText: 'https://maps.google.com/...',
                border: OutlineInputBorder(),
                filled: true,
                fillColor: Colors.white,
              ),
              validator: (v) {
                if ((_latController.text.trim().isEmpty || _lngController.text.trim().isEmpty) && (v == null || v.trim().isEmpty)) {
                  return 'Cần nhập vị trí GPS hoặc link Google Maps';
                }
                return null;
              },
            ),
            const SizedBox(height: 28),
            // Nút gửi báo cáo
            SizedBox(
              height: 48,
              child: ElevatedButton(
                onPressed: _isLoading ? null : _submit,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF10B981),
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                  textStyle: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                child: _isLoading
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(strokeWidth: 2, valueColor: AlwaysStoppedAnimation<Color>(Colors.white)),
                      )
                    : const Text('Gửi báo cáo'),
              ),
            ),
          ],
        ),
      ),
    );
  }
} 