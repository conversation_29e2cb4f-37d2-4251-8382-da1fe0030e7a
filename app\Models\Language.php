<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\Cache;

class Language extends Model
{
    protected $fillable = ['locale', 'name', 'is_default', 'is_active'];

    protected $casts = [
        'is_default' => 'boolean',
        'is_active' => 'boolean',
    ];

    protected static function boot()
    {
        parent::boot();

        // Ensure only one default language
        static::saving(function ($language) {
            if ($language->is_default) {
                // Remove default from all other languages
                static::where('id', '!=', $language->id)->update(['is_default' => false]);
                // Default language must be active
                $language->is_active = true;
            }
        });

        // Clear cache when language is saved or deleted
        static::saved(function () {
            Cache::forget('default_locale');
            Cache::forget('available_locales');
        });

        static::deleted(function () {
            Cache::forget('default_locale');
            Cache::forget('available_locales');
        });
    }

    /**
     * Get translations for this language
     */
    public function translations(): HasMany
    {
        return $this->hasMany(Translation::class, 'locale', 'locale');
    }

    /**
     * Get the default language
     */
    public static function getDefault(): ?Language
    {
        return cache()->rememberForever('default_language', function () {
            return static::where('is_default', true)->first();
        });
    }

    /**
     * Get all active languages
     */
    public static function getActive(): \Illuminate\Database\Eloquent\Collection
    {
        return cache()->rememberForever('active_languages', function () {
            return static::where('is_active', true)->orderBy('name')->get();
        });
    }

    /**
     * Get available locales
     */
    public static function getAvailableLocales(): array
    {
        return cache()->rememberForever('available_locales', function () {
            return static::where('is_active', true)->pluck('locale')->toArray();
        });
    }

    /**
     * Check if locale is available
     */
    public static function isLocaleAvailable(string $locale): bool
    {
        return in_array($locale, static::getAvailableLocales());
    }

    /**
     * Get default locale
     */
    public static function getDefaultLocale(): string
    {
        return cache()->rememberForever('default_locale', function () {
            return static::where('is_default', true)->value('locale') ?? config('app.locale');
        });
    }
}
